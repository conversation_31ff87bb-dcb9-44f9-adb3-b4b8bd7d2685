running develop
running egg_info
writing depth_tracker.egg-info/PKG-INFO
writing dependency_links to depth_tracker.egg-info/dependency_links.txt
writing entry points to depth_tracker.egg-info/entry_points.txt
writing requirements to depth_tracker.egg-info/requires.txt
writing top-level names to depth_tracker.egg-info/top_level.txt
reading manifest file 'depth_tracker.egg-info/SOURCES.txt'
writing manifest file 'depth_tracker.egg-info/SOURCES.txt'
running build_ext
Creating /Users/<USER>/Work/drill2/onboard/install/depth_tracker/lib/python3.11/site-packages/depth-tracker.egg-link (link to .)
Installing depth_tracker script to /Users/<USER>/Work/drill2/onboard/install/depth_tracker/lib/depth_tracker

Installed /Users/<USER>/Work/drill2/onboard/build/depth_tracker
running symlink_data
