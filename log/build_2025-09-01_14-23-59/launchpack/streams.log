[2.959s] Invoking command in '/Users/<USER>/Work/drill2/onboard/build/launchpack': PYTHONPATH=/Users/<USER>/Work/drill2/onboard/build/launchpack/prefix_override:/Users/<USER>/.ros2_venv/lib/python3.11/site-packages/colcon_core/task/python/colcon_distutils_commands:/Users/<USER>/Work/drill2/onboard/install/launchpack/lib/python3.11/site-packages:${PYTHONPATH} SHLVL=2 VIRTUAL_ENV_PROMPT=(.ros2_venv) _=/usr/bin/env /Users/<USER>/.ros2_venv/bin/python3 -W ignore:setup.py install is deprecated -W ignore:easy_install command is deprecated setup.py develop --editable --build-directory /Users/<USER>/Work/drill2/onboard/build/launchpack/build --no-deps symlink_data
[4.457s] running develop
[4.616s] running egg_info
[4.617s] writing launchpack.egg-info/PKG-INFO
[4.617s] writing dependency_links to launchpack.egg-info/dependency_links.txt
[4.617s] writing entry points to launchpack.egg-info/entry_points.txt
[4.617s] writing requirements to launchpack.egg-info/requires.txt
[4.617s] writing top-level names to launchpack.egg-info/top_level.txt
[4.618s] reading manifest file 'launchpack.egg-info/SOURCES.txt'
[4.618s] writing manifest file 'launchpack.egg-info/SOURCES.txt'
[4.677s] running build_ext
[4.677s] Creating /Users/<USER>/Work/drill2/onboard/install/launchpack/lib/python3.11/site-packages/launchpack.egg-link (link to .)
[4.678s] 
[4.678s] Installed /Users/<USER>/Work/drill2/onboard/build/launchpack
[4.679s] running symlink_data
[4.702s] Invoked command in '/Users/<USER>/Work/drill2/onboard/build/launchpack' returned '0': PYTHONPATH=/Users/<USER>/Work/drill2/onboard/build/launchpack/prefix_override:/Users/<USER>/.ros2_venv/lib/python3.11/site-packages/colcon_core/task/python/colcon_distutils_commands:/Users/<USER>/Work/drill2/onboard/install/launchpack/lib/python3.11/site-packages:${PYTHONPATH} SHLVL=2 VIRTUAL_ENV_PROMPT=(.ros2_venv) _=/usr/bin/env /Users/<USER>/.ros2_venv/bin/python3 -W ignore:setup.py install is deprecated -W ignore:easy_install command is deprecated setup.py develop --editable --build-directory /Users/<USER>/Work/drill2/onboard/build/launchpack/build --no-deps symlink_data
