running develop
running egg_info
writing base_node.egg-info/PKG-INFO
writing dependency_links to base_node.egg-info/dependency_links.txt
writing entry points to base_node.egg-info/entry_points.txt
writing requirements to base_node.egg-info/requires.txt
writing top-level names to base_node.egg-info/top_level.txt
reading manifest file 'base_node.egg-info/SOURCES.txt'
writing manifest file 'base_node.egg-info/SOURCES.txt'
running build_ext
Creating /Users/<USER>/Work/drill2/onboard/install/base_node/lib/python3.11/site-packages/base-node.egg-link (link to .)

Installed /Users/<USER>/Work/drill2/onboard/build/base_node
running symlink_data
