[1.641s] Invoking command in '/Users/<USER>/Work/drill2/onboard/build/drill_regulator': PYTHONPATH=/Users/<USER>/Work/drill2/onboard/build/drill_regulator/prefix_override:/Users/<USER>/.ros2_venv/lib/python3.11/site-packages/colcon_core/task/python/colcon_distutils_commands:/Users/<USER>/Work/drill2/onboard/install/drill_regulator/lib/python3.11/site-packages:${PYTHONPATH} SHLVL=2 VIRTUAL_ENV_PROMPT=(.ros2_venv) _=/usr/bin/env /Users/<USER>/.ros2_venv/bin/python3 -W ignore:setup.py install is deprecated -W ignore:easy_install command is deprecated setup.py develop --editable --build-directory /Users/<USER>/Work/drill2/onboard/build/drill_regulator/build --no-deps symlink_data
[2.446s] running develop
[2.729s] running egg_info
[2.730s] writing drill_regulator.egg-info/PKG-INFO
[2.730s] writing dependency_links to drill_regulator.egg-info/dependency_links.txt
[2.730s] writing entry points to drill_regulator.egg-info/entry_points.txt
[2.730s] writing requirements to drill_regulator.egg-info/requires.txt
[2.730s] writing top-level names to drill_regulator.egg-info/top_level.txt
[2.732s] reading manifest file 'drill_regulator.egg-info/SOURCES.txt'
[2.732s] writing manifest file 'drill_regulator.egg-info/SOURCES.txt'
[2.853s] running build_ext
[2.853s] Creating /Users/<USER>/Work/drill2/onboard/install/drill_regulator/lib/python3.11/site-packages/drill-regulator.egg-link (link to .)
[2.854s] Installing drill_regulator_node script to /Users/<USER>/Work/drill2/onboard/install/drill_regulator/lib/drill_regulator
[2.854s] 
[2.854s] Installed /Users/<USER>/Work/drill2/onboard/build/drill_regulator
[2.854s] running symlink_data
[2.980s] Invoked command in '/Users/<USER>/Work/drill2/onboard/build/drill_regulator' returned '0': PYTHONPATH=/Users/<USER>/Work/drill2/onboard/build/drill_regulator/prefix_override:/Users/<USER>/.ros2_venv/lib/python3.11/site-packages/colcon_core/task/python/colcon_distutils_commands:/Users/<USER>/Work/drill2/onboard/install/drill_regulator/lib/python3.11/site-packages:${PYTHONPATH} SHLVL=2 VIRTUAL_ENV_PROMPT=(.ros2_venv) _=/usr/bin/env /Users/<USER>/.ros2_venv/bin/python3 -W ignore:setup.py install is deprecated -W ignore:easy_install command is deprecated setup.py develop --editable --build-directory /Users/<USER>/Work/drill2/onboard/build/drill_regulator/build --no-deps symlink_data
