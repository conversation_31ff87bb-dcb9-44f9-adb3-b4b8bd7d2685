running develop
running egg_info
writing state_tracker.egg-info/PKG-INFO
writing dependency_links to state_tracker.egg-info/dependency_links.txt
writing entry points to state_tracker.egg-info/entry_points.txt
writing requirements to state_tracker.egg-info/requires.txt
writing top-level names to state_tracker.egg-info/top_level.txt
reading manifest file 'state_tracker.egg-info/SOURCES.txt'
writing manifest file 'state_tracker.egg-info/SOURCES.txt'
running build_ext
Creating /Users/<USER>/Work/drill2/onboard/install/state_tracker/lib/python3.11/site-packages/state-tracker.egg-link (link to .)
Installing state_tracker script to /Users/<USER>/Work/drill2/onboard/install/state_tracker/lib/state_tracker

Installed /Users/<USER>/Work/drill2/onboard/build/state_tracker
running symlink_data
