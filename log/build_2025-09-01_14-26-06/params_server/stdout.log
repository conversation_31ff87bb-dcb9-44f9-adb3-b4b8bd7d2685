running develop
running egg_info
writing params_server.egg-info/PKG-INFO
writing dependency_links to params_server.egg-info/dependency_links.txt
writing entry points to params_server.egg-info/entry_points.txt
writing requirements to params_server.egg-info/requires.txt
writing top-level names to params_server.egg-info/top_level.txt
reading manifest file 'params_server.egg-info/SOURCES.txt'
writing manifest file 'params_server.egg-info/SOURCES.txt'
running build_ext
Creating /Users/<USER>/Work/drill2/onboard/install/params_server/lib/python3.11/site-packages/params-server.egg-link (link to .)
Installing params_server script to /Users/<USER>/Work/drill2/onboard/install/params_server/lib/params_server

Installed /Users/<USER>/Work/drill2/onboard/build/params_server
running symlink_data
