running develop
running egg_info
writing drill_regulator.egg-info/PKG-INFO
writing dependency_links to drill_regulator.egg-info/dependency_links.txt
writing entry points to drill_regulator.egg-info/entry_points.txt
writing requirements to drill_regulator.egg-info/requires.txt
writing top-level names to drill_regulator.egg-info/top_level.txt
reading manifest file 'drill_regulator.egg-info/SOURCES.txt'
writing manifest file 'drill_regulator.egg-info/SOURCES.txt'
running build_ext
Creating /Users/<USER>/Work/drill2/onboard/install/drill_regulator/lib/python3.11/site-packages/drill-regulator.egg-link (link to .)
Installing drill_regulator_node script to /Users/<USER>/Work/drill2/onboard/install/drill_regulator/lib/drill_regulator

Installed /Users/<USER>/Work/drill2/onboard/build/drill_regulator
running symlink_data
