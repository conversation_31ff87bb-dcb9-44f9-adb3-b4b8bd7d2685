[3.993s] Invoking command in '/Users/<USER>/Work/drill2/onboard/build/drill_regulator': PYTHONPATH=/Users/<USER>/Work/drill2/onboard/build/drill_regulator/prefix_override:/Users/<USER>/.ros2_venv/lib/python3.11/site-packages/colcon_core/task/python/colcon_distutils_commands:/Users/<USER>/Work/drill2/onboard/install/drill_regulator/lib/python3.11/site-packages:${PYTHONPATH} SHLVL=2 VIRTUAL_ENV_PROMPT=(.ros2_venv) _=/usr/bin/env /Users/<USER>/.ros2_venv/bin/python3 -W ignore:setup.py install is deprecated -W ignore:easy_install command is deprecated setup.py develop --editable --build-directory /Users/<USER>/Work/drill2/onboard/build/drill_regulator/build --no-deps symlink_data
[5.750s] running develop
[5.998s] running egg_info
[5.998s] writing drill_regulator.egg-info/PKG-INFO
[5.999s] writing dependency_links to drill_regulator.egg-info/dependency_links.txt
[5.999s] writing entry points to drill_regulator.egg-info/entry_points.txt
[5.999s] writing requirements to drill_regulator.egg-info/requires.txt
[5.999s] writing top-level names to drill_regulator.egg-info/top_level.txt
[6.001s] reading manifest file 'drill_regulator.egg-info/SOURCES.txt'
[6.003s] writing manifest file 'drill_regulator.egg-info/SOURCES.txt'
[6.108s] running build_ext
[6.109s] Creating /Users/<USER>/Work/drill2/onboard/install/drill_regulator/lib/python3.11/site-packages/drill-regulator.egg-link (link to .)
[6.110s] Installing drill_regulator_node script to /Users/<USER>/Work/drill2/onboard/install/drill_regulator/lib/drill_regulator
[6.110s] 
[6.110s] Installed /Users/<USER>/Work/drill2/onboard/build/drill_regulator
[6.110s] running symlink_data
[6.194s] Invoked command in '/Users/<USER>/Work/drill2/onboard/build/drill_regulator' returned '0': PYTHONPATH=/Users/<USER>/Work/drill2/onboard/build/drill_regulator/prefix_override:/Users/<USER>/.ros2_venv/lib/python3.11/site-packages/colcon_core/task/python/colcon_distutils_commands:/Users/<USER>/Work/drill2/onboard/install/drill_regulator/lib/python3.11/site-packages:${PYTHONPATH} SHLVL=2 VIRTUAL_ENV_PROMPT=(.ros2_venv) _=/usr/bin/env /Users/<USER>/.ros2_venv/bin/python3 -W ignore:setup.py install is deprecated -W ignore:easy_install command is deprecated setup.py develop --editable --build-directory /Users/<USER>/Work/drill2/onboard/build/drill_regulator/build --no-deps symlink_data
