[6.026s] Invoking command in '/Users/<USER>/Work/drill2/onboard/build/launchpack': PYTHONPATH=/Users/<USER>/Work/drill2/onboard/build/launchpack/prefix_override:/Users/<USER>/.ros2_venv/lib/python3.11/site-packages/colcon_core/task/python/colcon_distutils_commands:/Users/<USER>/Work/drill2/onboard/install/launchpack/lib/python3.11/site-packages:${PYTHONPATH} SHLVL=2 VIRTUAL_ENV_PROMPT=(.ros2_venv) _=/usr/bin/env /Users/<USER>/.ros2_venv/bin/python3 -W ignore:setup.py install is deprecated -W ignore:easy_install command is deprecated setup.py develop --editable --build-directory /Users/<USER>/Work/drill2/onboard/build/launchpack/build --no-deps symlink_data
[6.896s] running develop
[7.170s] running egg_info
[7.170s] writing launchpack.egg-info/PKG-INFO
[7.170s] writing dependency_links to launchpack.egg-info/dependency_links.txt
[7.170s] writing entry points to launchpack.egg-info/entry_points.txt
[7.171s] writing requirements to launchpack.egg-info/requires.txt
[7.171s] writing top-level names to launchpack.egg-info/top_level.txt
[7.171s] reading manifest file 'launchpack.egg-info/SOURCES.txt'
[7.171s] writing manifest file 'launchpack.egg-info/SOURCES.txt'
[7.281s] running build_ext
[7.284s] Creating /Users/<USER>/Work/drill2/onboard/install/launchpack/lib/python3.11/site-packages/launchpack.egg-link (link to .)
[7.285s] 
[7.285s] Installed /Users/<USER>/Work/drill2/onboard/build/launchpack
[7.285s] running symlink_data
[7.360s] Invoked command in '/Users/<USER>/Work/drill2/onboard/build/launchpack' returned '0': PYTHONPATH=/Users/<USER>/Work/drill2/onboard/build/launchpack/prefix_override:/Users/<USER>/.ros2_venv/lib/python3.11/site-packages/colcon_core/task/python/colcon_distutils_commands:/Users/<USER>/Work/drill2/onboard/install/launchpack/lib/python3.11/site-packages:${PYTHONPATH} SHLVL=2 VIRTUAL_ENV_PROMPT=(.ros2_venv) _=/usr/bin/env /Users/<USER>/.ros2_venv/bin/python3 -W ignore:setup.py install is deprecated -W ignore:easy_install command is deprecated setup.py develop --editable --build-directory /Users/<USER>/Work/drill2/onboard/build/launchpack/build --no-deps symlink_data
