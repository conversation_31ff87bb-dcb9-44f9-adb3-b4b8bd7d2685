running develop
running egg_info
writing path_follower.egg-info/PKG-INFO
writing dependency_links to path_follower.egg-info/dependency_links.txt
writing entry points to path_follower.egg-info/entry_points.txt
writing requirements to path_follower.egg-info/requires.txt
writing top-level names to path_follower.egg-info/top_level.txt
reading manifest file 'path_follower.egg-info/SOURCES.txt'
writing manifest file 'path_follower.egg-info/SOURCES.txt'
running build_ext
Creating /Users/<USER>/Work/drill2/onboard/install/path_follower/lib/python3.11/site-packages/path-follower.egg-link (link to .)
Installing path_follower script to /Users/<USER>/Work/drill2/onboard/install/path_follower/lib/path_follower
Installing path_follower_node script to /Users/<USER>/Work/drill2/onboard/install/path_follower/lib/path_follower

Installed /Users/<USER>/Work/drill2/onboard/build/path_follower
running symlink_data
