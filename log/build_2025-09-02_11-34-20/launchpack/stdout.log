running develop
running egg_info
writing launchpack.egg-info/PKG-INFO
writing dependency_links to launchpack.egg-info/dependency_links.txt
writing entry points to launchpack.egg-info/entry_points.txt
writing requirements to launchpack.egg-info/requires.txt
writing top-level names to launchpack.egg-info/top_level.txt
reading manifest file 'launchpack.egg-info/SOURCES.txt'
writing manifest file 'launchpack.egg-info/SOURCES.txt'
running build_ext
Creating /Users/<USER>/Work/drill2/onboard/install/launchpack/lib/python3.11/site-packages/launchpack.egg-link (link to .)

Installed /Users/<USER>/Work/drill2/onboard/build/launchpack
running symlink_data
