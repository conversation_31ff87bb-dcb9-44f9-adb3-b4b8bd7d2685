running develop
running egg_info
writing can_decoder.egg-info/PKG-INFO
writing dependency_links to can_decoder.egg-info/dependency_links.txt
writing entry points to can_decoder.egg-info/entry_points.txt
writing requirements to can_decoder.egg-info/requires.txt
writing top-level names to can_decoder.egg-info/top_level.txt
reading manifest file 'can_decoder.egg-info/SOURCES.txt'
writing manifest file 'can_decoder.egg-info/SOURCES.txt'
running build_ext
Creating /Users/<USER>/Work/drill2/onboard/install/can_decoder/lib/python3.11/site-packages/can-decoder.egg-link (link to .)
Installing can_decoder script to /Users/<USER>/Work/drill2/onboard/install/can_decoder/lib/can_decoder

Installed /Users/<USER>/Work/drill2/onboard/build/can_decoder
running symlink_data
