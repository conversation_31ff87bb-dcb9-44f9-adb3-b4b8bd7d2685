[1.351s] Invoking command in '/Users/<USER>/Work/drill2/onboard/build/drill_regulator': PYTHONPATH=/Users/<USER>/Work/drill2/onboard/build/drill_regulator/prefix_override:/Users/<USER>/.ros2_venv/lib/python3.11/site-packages/colcon_core/task/python/colcon_distutils_commands:/Users/<USER>/Work/drill2/onboard/install/drill_regulator/lib/python3.11/site-packages:${PYTHONPATH} SHLVL=2 VIRTUAL_ENV_PROMPT=(.ros2_venv) _=/usr/bin/env /Users/<USER>/.ros2_venv/bin/python3 -W ignore:setup.py install is deprecated -W ignore:easy_install command is deprecated setup.py develop --editable --build-directory /Users/<USER>/Work/drill2/onboard/build/drill_regulator/build --no-deps symlink_data
[2.160s] running develop
[2.480s] running egg_info
[2.480s] writing drill_regulator.egg-info/PKG-INFO
[2.483s] writing dependency_links to drill_regulator.egg-info/dependency_links.txt
[2.484s] writing entry points to drill_regulator.egg-info/entry_points.txt
[2.484s] writing requirements to drill_regulator.egg-info/requires.txt
[2.484s] writing top-level names to drill_regulator.egg-info/top_level.txt
[2.606s] reading manifest file 'drill_regulator.egg-info/SOURCES.txt'
[2.608s] writing manifest file 'drill_regulator.egg-info/SOURCES.txt'
[3.021s] running build_ext
[3.021s] Creating /Users/<USER>/Work/drill2/onboard/install/drill_regulator/lib/python3.11/site-packages/drill-regulator.egg-link (link to .)
[3.058s] Installing drill_regulator_node script to /Users/<USER>/Work/drill2/onboard/install/drill_regulator/lib/drill_regulator
[3.060s] 
[3.061s] Installed /Users/<USER>/Work/drill2/onboard/build/drill_regulator
[3.061s] running symlink_data
[3.397s] Invoked command in '/Users/<USER>/Work/drill2/onboard/build/drill_regulator' returned '0': PYTHONPATH=/Users/<USER>/Work/drill2/onboard/build/drill_regulator/prefix_override:/Users/<USER>/.ros2_venv/lib/python3.11/site-packages/colcon_core/task/python/colcon_distutils_commands:/Users/<USER>/Work/drill2/onboard/install/drill_regulator/lib/python3.11/site-packages:${PYTHONPATH} SHLVL=2 VIRTUAL_ENV_PROMPT=(.ros2_venv) _=/usr/bin/env /Users/<USER>/.ros2_venv/bin/python3 -W ignore:setup.py install is deprecated -W ignore:easy_install command is deprecated setup.py develop --editable --build-directory /Users/<USER>/Work/drill2/onboard/build/drill_regulator/build --no-deps symlink_data
