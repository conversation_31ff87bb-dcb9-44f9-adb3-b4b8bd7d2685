running develop
running egg_info
writing remote_connector.egg-info/PKG-INFO
writing dependency_links to remote_connector.egg-info/dependency_links.txt
writing entry points to remote_connector.egg-info/entry_points.txt
writing requirements to remote_connector.egg-info/requires.txt
writing top-level names to remote_connector.egg-info/top_level.txt
reading manifest file 'remote_connector.egg-info/SOURCES.txt'
writing manifest file 'remote_connector.egg-info/SOURCES.txt'
running build_ext
Creating /Users/<USER>/Work/drill2/onboard/install/remote_connector/lib/python3.11/site-packages/remote-connector.egg-link (link to .)
Installing remote_connector script to /Users/<USER>/Work/drill2/onboard/install/remote_connector/lib/remote_connector

Installed /Users/<USER>/Work/drill2/onboard/build/remote_connector
running symlink_data
