Invoking command in '/Users/<USER>/Work/drill2/onboard/build/tracks_regulator': AMENT_PREFIX_PATH=/Users/<USER>/Work/drill2/onboard/install/base_node:/Users/<USER>/Work/drill2/onboard/install/remote_connector:/Users/<USER>/Work/drill2/onboard/install/tracks_regulator:/Users/<USER>/Work/drill2/onboard/install/state_tracker:/Users/<USER>/Work/drill2/onboard/install/rtk_connector:/Users/<USER>/Work/drill2/onboard/install/path_follower:/Users/<USER>/Work/drill2/onboard/install/params_server:/Users/<USER>/Work/drill2/onboard/install/leveler:/Users/<USER>/Work/drill2/onboard/install/launchpack:/Users/<USER>/Work/drill2/onboard/install/driller:/Users/<USER>/Work/drill2/onboard/install/depth_tracker:/Users/<USER>/Work/drill2/onboard/install/can_encoder:/Users/<USER>/Work/drill2/onboard/install/can_decoder:/Users/<USER>/Work/drill2/onboard/install/arm_controller:/Users/<USER>/Work/drill2/onboard/install/drill_msgs:/Users/<USER>/Work/drill2/onboard/install/can_msgs:/Users/<USER>/ros2_jazzy/install/rosbag2:/Users/<USER>/ros2_jazzy/install/rosbag2_storage_default_plugins:/Users/<USER>/ros2_jazzy/install/rosbag2_storage_mcap:/Users/<USER>/ros2_jazzy/install/rosbag2_compression_zstd:/Users/<USER>/ros2_jazzy/install/mcap_vendor:/Users/<USER>/ros2_jazzy/install/zstd_vendor:/Users/<USER>/ros2_jazzy/install/rviz_visual_testing_framework:/Users/<USER>/ros2_jazzy/install/rviz2:/Users/<USER>/ros2_jazzy/install/rviz_default_plugins:/Users/<USER>/ros2_jazzy/install/rviz_common:/Users/<USER>/ros2_jazzy/install/rosbag2_examples_py:/Users/<USER>/ros2_jazzy/install/launch_testing_examples:/Users/<USER>/ros2_jazzy/install/ros2bag:/Users/<USER>/ros2_jazzy/install/rosbag2_py:/Users/<USER>/ros2_jazzy/install/rosbag2_transport:/Users/<USER>/ros2_jazzy/install/rosbag2_storage_sqlite3:/Users/<USER>/ros2_jazzy/install/rosbag2_examples_cpp:/Users/<USER>/ros2_jazzy/install/rosbag2_compression:/Users/<USER>/ros2_jazzy/install/rosbag2_cpp:/Users/<USER>/ros2_jazzy/install/rosbag2_storage:/Users/<USER>/ros2_jazzy/install/image_common:/Users/<USER>/ros2_jazzy/install/camera_info_manager:/Users/<USER>/ros2_jazzy/install/camera_calibration_parsers:/Users/<USER>/ros2_jazzy/install/yaml_cpp_vendor:/Users/<USER>/ros2_jazzy/install/interactive_markers:/Users/<USER>/ros2_jazzy/install/common_interfaces:/Users/<USER>/ros2_jazzy/install/visualization_msgs:/Users/<USER>/ros2_jazzy/install/dummy_robot_bringup:/Users/<USER>/ros2_jazzy/install/robot_state_publisher:/Users/<USER>/ros2_jazzy/install/kdl_parser:/Users/<USER>/ros2_jazzy/install/urdf:/Users/<USER>/ros2_jazzy/install/urdf_parser_plugin:/Users/<USER>/ros2_jazzy/install/turtlesim:/Users/<USER>/ros2_jazzy/install/trajectory_msgs:/Users/<USER>/ros2_jazzy/install/tracetools_test:/Users/<USER>/ros2_jazzy/install/tracetools_launch:/Users/<USER>/ros2_jazzy/install/topic_statistics_demo:/Users/<USER>/ros2_jazzy/install/topic_monitor:/Users/<USER>/ros2_jazzy/install/geometry2:/Users/<USER>/ros2_jazzy/install/tf2_tools:/Users/<USER>/ros2_jazzy/install/tf2_sensor_msgs:/Users/<USER>/ros2_jazzy/install/test_tf2:/Users/<USER>/ros2_jazzy/install/tf2_kdl:/Users/<USER>/ros2_jazzy/install/tf2_geometry_msgs:/Users/<USER>/ros2_jazzy/install/examples_tf2_py:/Users/<USER>/ros2_jazzy/install/tf2_ros_py:/Users/<USER>/ros2_jazzy/install/tf2_eigen:/Users/<USER>/ros2_jazzy/install/tf2_bullet:/Users/<USER>/ros2_jazzy/install/tf2_ros:/Users/<USER>/ros2_jazzy/install/tf2_py:/Users/<USER>/ros2_jazzy/install/tf2_msgs:/Users/<USER>/ros2_jazzy/install/tf2_eigen_kdl:/Users/<USER>/ros2_jazzy/install/laser_geometry:/Users/<USER>/ros2_jazzy/install/tf2:/Users/<USER>/ros2_jazzy/install/test_tracetools:/Users/<USER>/ros2_jazzy/install/rosbag2_test_common:/Users/<USER>/ros2_jazzy/install/test_msgs:/Users/<USER>/ros2_jazzy/install/test_communication:/Users/<USER>/ros2_jazzy/install/stereo_msgs:/Users/<USER>/ros2_jazzy/install/std_srvs:/Users/<USER>/ros2_jazzy/install/shape_msgs:/Users/<USER>/ros2_jazzy/install/examples_rclpy_pointcloud_publisher:/Users/<USER>/ros2_jazzy/install/sensor_msgs_py:/Users/<USER>/ros2_jazzy/install/quality_of_service_demo_py:/Users/<USER>/ros2_jazzy/install/quality_of_service_demo_cpp:/Users/<USER>/ros2_jazzy/install/point_cloud_transport_py:/Users/<USER>/ros2_jazzy/install/point_cloud_transport:/Users/<USER>/ros2_jazzy/install/map_msgs:/Users/<USER>/ros2_jazzy/install/intra_process_demo:/Users/<USER>/ros2_jazzy/install/image_transport:/Users/<USER>/ros2_jazzy/install/image_tools:/Users/<USER>/ros2_jazzy/install/dummy_sensors:/Users/<USER>/ros2_jazzy/install/camera_info_manager_py:/Users/<USER>/ros2_jazzy/install/sensor_msgs:/Users/<USER>/ros2_jazzy/install/ros2cli_common_extensions:/Users/<USER>/ros2_jazzy/install/ros2doctor:/Users/<USER>/ros2_jazzy/install/dummy_map_server:/Users/<USER>/ros2_jazzy/install/nav_msgs:/Users/<USER>/ros2_jazzy/install/message_filters:/Users/<USER>/ros2_jazzy/install/logging_demo:/Users/<USER>/ros2_jazzy/install/lifecycle_py:/Users/<USER>/ros2_jazzy/install/lifecycle:/Users/<USER>/ros2_jazzy/install/diagnostic_msgs:/Users/<USER>/ros2_jazzy/install/geometry_msgs:/Users/<USER>/ros2_jazzy/install/examples_rclpy_minimal_subscriber:/Users/<USER>/ros2_jazzy/install/examples_rclpy_minimal_service:/Users/<USER>/ros2_jazzy/install/examples_rclpy_minimal_publisher:/Users/<USER>/ros2_jazzy/install/examples_rclpy_minimal_client:/Users/<USER>/ros2_jazzy/install/examples_rclpy_executors:/Users/<USER>/ros2_jazzy/install/examples_rclcpp_wait_set:/Users/<USER>/ros2_jazzy/install/examples_rclcpp_multithreaded_executor:/Users/<USER>/ros2_jazzy/install/examples_rclcpp_minimal_subscriber:/Users/<USER>/ros2_jazzy/install/examples_rclcpp_minimal_publisher:/Users/<USER>/ros2_jazzy/install/examples_rclcpp_minimal_composition:/Users/<USER>/ros2_jazzy/install/examples_rclcpp_cbg_executor:/Users/<USER>/ros2_jazzy/install/demo_nodes_py:/Users/<USER>/ros2_jazzy/install/demo_nodes_cpp_native:/Users/<USER>/ros2_jazzy/install/demo_nodes_cpp:/Users/<USER>/ros2_jazzy/install/composition:/Users/<USER>/ros2_jazzy/install/actionlib_msgs:/Users/<USER>/ros2_jazzy/install/std_msgs:/Users/<USER>/ros2_jazzy/install/ros2lifecycle_test_fixtures:/Users/<USER>/ros2_jazzy/install/ros2component:/Users/<USER>/ros2_jazzy/install/rclcpp_lifecycle:/Users/<USER>/ros2_jazzy/install/action_tutorials_cpp:/Users/<USER>/ros2_jazzy/install/rclcpp_components:/Users/<USER>/ros2_jazzy/install/examples_rclcpp_minimal_action_server:/Users/<USER>/ros2_jazzy/install/examples_rclcpp_minimal_action_client:/Users/<USER>/ros2_jazzy/install/rclcpp_action:/Users/<USER>/ros2_jazzy/install/examples_rclcpp_minimal_timer:/Users/<USER>/ros2_jazzy/install/examples_rclcpp_minimal_service:/Users/<USER>/ros2_jazzy/install/examples_rclcpp_minimal_client:/Users/<USER>/ros2_jazzy/install/examples_rclcpp_async_client:/Users/<USER>/ros2_jazzy/install/rclcpp:/Users/<USER>/ros2_jazzy/install/libstatistics_collector:/Users/<USER>/ros2_jazzy/install/statistics_msgs:/Users/<USER>/ros2_jazzy/install/sros2_cmake:/Users/<USER>/ros2_jazzy/install/sros2:/Users/<USER>/ros2_jazzy/install/ros_testing:/Users/<USER>/ros2_jazzy/install/ros2trace:/Users/<USER>/ros2_jazzy/install/ros2topic:/Users/<USER>/ros2_jazzy/install/ros2test:/Users/<USER>/ros2_jazzy/install/ros2param:/Users/<USER>/ros2_jazzy/install/ros2lifecycle:/Users/<USER>/ros2_jazzy/install/ros2service:/Users/<USER>/ros2_jazzy/install/ros2run:/Users/<USER>/ros2_jazzy/install/ros2launch:/Users/<USER>/ros2_jazzy/install/ros2pkg:/Users/<USER>/ros2_jazzy/install/ros2node:/Users/<USER>/ros2_jazzy/install/ros2multicast:/Users/<USER>/ros2_jazzy/install/ros2interface:/Users/<USER>/ros2_jazzy/install/ros2action:/Users/<USER>/ros2_jazzy/install/ros2cli:/Users/<USER>/ros2_jazzy/install/launch_testing_ros:/Users/<USER>/ros2_jazzy/install/launch_ros:/Users/<USER>/ros2_jazzy/install/examples_rclpy_minimal_action_server:/Users/<USER>/ros2_jazzy/install/examples_rclpy_minimal_action_client:/Users/<USER>/ros2_jazzy/install/examples_rclpy_guard_conditions:/Users/<USER>/ros2_jazzy/install/action_tutorials_py:/Users/<USER>/ros2_jazzy/install/rclpy:/Users/<USER>/ros2_jazzy/install/rosgraph_msgs:/Users/<USER>/ros2_jazzy/install/rosbag2_interfaces:/Users/<USER>/ros2_jazzy/install/ros2cli_test_interfaces:/Users/<USER>/ros2_jazzy/install/rcl_lifecycle:/Users/<USER>/ros2_jazzy/install/rcl_action:/Users/<USER>/ros2_jazzy/install/rcl:/Users/<USER>/ros2_jazzy/install/rmw_implementation:/Users/<USER>/ros2_jazzy/install/rmw_fastrtps_dynamic_cpp:/Users/<USER>/ros2_jazzy/install/rmw_fastrtps_cpp:/Users/<USER>/ros2_jazzy/install/rmw_fastrtps_shared_cpp:/Users/<USER>/ros2_jazzy/install/rmw_cyclonedds_cpp:/Users/<USER>/ros2_jazzy/install/rmw_connextddsmicro:/Users/<USER>/ros2_jazzy/install/rmw_connextdds:/Users/<USER>/ros2_jazzy/install/rmw_connextdds_common:/Users/<USER>/ros2_jazzy/install/rmw_dds_common:/Users/<USER>/ros2_jazzy/install/composition_interfaces:/Users/<USER>/ros2_jazzy/install/rcl_interfaces:/Users/<USER>/ros2_jazzy/install/pendulum_msgs:/Users/<USER>/ros2_jazzy/install/lifecycle_msgs:/Users/<USER>/ros2_jazzy/install/example_interfaces:/Users/<USER>/ros2_jazzy/install/action_tutorials_interfaces:/Users/<USER>/ros2_jazzy/install/rosidl_default_runtime:/Users/<USER>/ros2_jazzy/install/rosidl_default_generators:/Users/<USER>/ros2_jazzy/install/action_msgs:/Users/<USER>/ros2_jazzy/install/unique_identifier_msgs:/Users/<USER>/ros2_jazzy/install/type_description_interfaces:/Users/<USER>/ros2_jazzy/install/service_msgs:/Users/<USER>/ros2_jazzy/install/builtin_interfaces:/Users/<USER>/ros2_jazzy/install/rosidl_core_runtime:/Users/<USER>/ros2_jazzy/install/rosidl_core_generators:/Users/<USER>/ros2_jazzy/install/rosidl_generator_py:/Users/<USER>/ros2_jazzy/install/ament_lint_common:/Users/<USER>/ros2_jazzy/install/ament_cmake_uncrustify:/Users/<USER>/ros2_jazzy/install/ament_uncrustify:/Users/<USER>/ros2_jazzy/install/uncrustify_vendor:/Users/<USER>/ros2_jazzy/install/tracetools_trace:/Users/<USER>/ros2_jazzy/install/tracetools_read:/Users/<USER>/ros2_jazzy/install/tracetools:/Users/<USER>/ros2_jazzy/install/pluginlib:/Users/<USER>/ros2_jazzy/install/tinyxml2_vendor:/Users/<USER>/ros2_jazzy/install/test_tracetools_launch:/Users/<USER>/ros2_jazzy/install/test_security:/Users/<USER>/ros2_jazzy/install/test_ros2trace:/Users/<USER>/ros2_jazzy/install/test_rmw_implementation:/Users/<USER>/ros2_jazzy/install/test_rclcpp:/Users/<USER>/ros2_jazzy/install/test_quality_of_service:/Users/<USER>/ros2_jazzy/install/test_launch_testing:/Users/<USER>/ros2_jazzy/install/test_launch_ros:/Users/<USER>/ros2_jazzy/install/test_interface_files:/Users/<USER>/ros2_jazzy/install/test_cli_remapping:/Users/<USER>/ros2_jazzy/install/test_cli:/Users/<USER>/ros2_jazzy/install/tango_icons_vendor:/Users/<USER>/ros2_jazzy/install/sqlite3_vendor:/Users/<USER>/ros2_jazzy/install/rcl_logging_spdlog:/Users/<USER>/ros2_jazzy/install/spdlog_vendor:/Users/<USER>/ros2_jazzy/install/shared_queues_vendor:/Users/<USER>/ros2_jazzy/install/rviz_rendering_tests:/Users/<USER>/ros2_jazzy/install/rviz_rendering:/Users/<USER>/ros2_jazzy/install/rviz_ogre_vendor:/Users/<USER>/ros2_jazzy/install/rviz_assimp_vendor:/Users/<USER>/ros2_jazzy/install/rti_connext_dds_cmake_module:/Users/<USER>/ros2_jazzy/install/lttngpy:/Users/<USER>/ros2_jazzy/install/rpyutils:/Users/<USER>/ros2_jazzy/install/rosidl_typesupport_tests:/Users/<USER>/ros2_jazzy/install/rosidl_typesupport_introspection_tests:/Users/<USER>/ros2_jazzy/install/rosidl_typesupport_cpp:/Users/<USER>/ros2_jazzy/install/rosidl_typesupport_introspection_cpp:/Users/<USER>/ros2_jazzy/install/rosidl_typesupport_c:/Users/<USER>/ros2_jazzy/install/rosidl_typesupport_introspection_c:/Users/<USER>/ros2_jazzy/install/rosidl_typesupport_fastrtps_c:/Users/<USER>/ros2_jazzy/install/rosidl_typesupport_fastrtps_cpp:/Users/<USER>/ros2_jazzy/install/rosidl_generator_cpp:/Users/<USER>/ros2_jazzy/install/rosidl_runtime_cpp:/Users/<USER>/ros2_jazzy/install/rosidl_dynamic_typesupport_fastrtps:/Users/<USER>/ros2_jazzy/install/rcl_yaml_param_parser:/Users/<USER>/ros2_jazzy/install/rmw:/Users/<USER>/ros2_jazzy/install/rosidl_dynamic_typesupport:/Users/<USER>/ros2_jazzy/install/rosidl_runtime_c:/Users/<USER>/ros2_jazzy/install/rosidl_generator_c:/Users/<USER>/ros2_jazzy/install/rosidl_typesupport_interface:/Users/<USER>/ros2_jazzy/install/rosidl_runtime_py:/Users/<USER>/ros2_jazzy/install/rosidl_generator_dds_idl:/Users/<USER>/ros2_jazzy/install/rosidl_cmake:/Users/<USER>/ros2_jazzy/install/rosidl_pycommon:/Users/<USER>/ros2_jazzy/install/rosidl_generator_type_description:/Users/<USER>/ros2_jazzy/install/rosidl_parser:/Users/<USER>/ros2_jazzy/install/rosidl_generator_tests:/Users/<USER>/ros2_jazzy/install/rosidl_adapter:/Users/<USER>/ros2_jazzy/install/rosidl_cli:/Users/<USER>/ros2_jazzy/install/rosbag2_tests:/Users/<USER>/ros2_jazzy/install/rosbag2_test_msgdefs:/Users/<USER>/ros2_jazzy/install/ros_environment:/Users/<USER>/ros2_jazzy/install/rmw_implementation_cmake:/Users/<USER>/ros2_jazzy/install/resource_retriever:/Users/<USER>/ros2_jazzy/install/class_loader:/Users/<USER>/ros2_jazzy/install/rcpputils:/Users/<USER>/ros2_jazzy/install/rcl_logging_noop:/Users/<USER>/ros2_jazzy/install/rcl_logging_interface:/Users/<USER>/ros2_jazzy/install/rcutils:/Users/<USER>/ros2_jazzy/install/python_qt_binding:/Users/<USER>/ros2_jazzy/install/python_orocos_kdl_vendor:/Users/<USER>/ros2_jazzy/install/launch_testing_ament_cmake:/Users/<USER>/ros2_jazzy/install/python_cmake_module:/Users/<USER>/ros2_jazzy/install/pybind11_vendor:/Users/<USER>/ros2_jazzy/install/performance_test_fixture:/Users/<USER>/ros2_jazzy/install/launch_pytest:/Users/<USER>/ros2_jazzy/install/launch_testing:/Users/<USER>/ros2_jazzy/install/launch_yaml:/Users/<USER>/ros2_jazzy/install/launch_xml:/Users/<USER>/ros2_jazzy/install/launch:/Users/<USER>/ros2_jazzy/install/osrf_pycommon:/Users/<USER>/ros2_jazzy/install/orocos_kdl_vendor:/Users/<USER>/ros2_jazzy/install/mimick_vendor:/Users/<USER>/ros2_jazzy/install/libyaml_vendor:/Users/<USER>/ros2_jazzy/install/liblz4_vendor:/Users/<USER>/ros2_jazzy/install/libcurl_vendor:/Users/<USER>/ros2_jazzy/install/keyboard_handler:/Users/<USER>/ros2_jazzy/install/gz_math_vendor:/Users/<USER>/ros2_jazzy/install/gz_utils_vendor:/Users/<USER>/ros2_jazzy/install/gz_cmake_vendor:/Users/<USER>/ros2_jazzy/install/ament_cmake_ros:/Users/<USER>/ros2_jazzy/install/ament_cmake_auto:/Users/<USER>/ros2_jazzy/install/ament_cmake_gmock:/Users/<USER>/ros2_jazzy/install/ament_cmake_gtest:/Users/<USER>/ros2_jazzy/install/ament_cmake_google_benchmark:/Users/<USER>/ros2_jazzy/install/fastrtps_cmake_module:/Users/<USER>/ros2_jazzy/install/eigen3_cmake_module:/Users/<USER>/ros2_jazzy/install/domain_coordinator:/Users/<USER>/ros2_jazzy/install/console_bridge_vendor:/Users/<USER>/ros2_jazzy/install/ament_cmake_xmllint:/Users/<USER>/ros2_jazzy/install/ament_xmllint:/Users/<USER>/ros2_jazzy/install/ament_cmake_pyflakes:/Users/<USER>/ros2_jazzy/install/ament_pyflakes:/Users/<USER>/ros2_jazzy/install/ament_cmake_pycodestyle:/Users/<USER>/ros2_jazzy/install/ament_pycodestyle:/Users/<USER>/ros2_jazzy/install/ament_cmake_pep257:/Users/<USER>/ros2_jazzy/install/ament_pep257:/Users/<USER>/ros2_jazzy/install/ament_cmake_pclint:/Users/<USER>/ros2_jazzy/install/ament_pclint:/Users/<USER>/ros2_jazzy/install/ament_lint_auto:/Users/<USER>/ros2_jazzy/install/ament_cmake:/Users/<USER>/ros2_jazzy/install/ament_cmake_version:/Users/<USER>/ros2_jazzy/install/ament_cmake_vendor_package:/Users/<USER>/ros2_jazzy/install/ament_cmake_pytest:/Users/<USER>/ros2_jazzy/install/ament_cmake_mypy:/Users/<USER>/ros2_jazzy/install/ament_cmake_lint_cmake:/Users/<USER>/ros2_jazzy/install/ament_cmake_flake8:/Users/<USER>/ros2_jazzy/install/ament_cmake_cpplint:/Users/<USER>/ros2_jazzy/install/ament_cmake_cppcheck:/Users/<USER>/ros2_jazzy/install/ament_cmake_copyright:/Users/<USER>/ros2_jazzy/install/ament_cmake_clang_tidy:/Users/<USER>/ros2_jazzy/install/ament_cmake_clang_format:/Users/<USER>/ros2_jazzy/install/ament_cmake_test:/Users/<USER>/ros2_jazzy/install/ament_cmake_target_dependencies:/Users/<USER>/ros2_jazzy/install/ament_cmake_python:/Users/<USER>/ros2_jazzy/install/ament_cmake_export_dependencies:/Users/<USER>/ros2_jazzy/install/ament_cmake_libraries:/Users/<USER>/ros2_jazzy/install/ament_cmake_include_directories:/Users/<USER>/ros2_jazzy/install/ament_cmake_gen_version_h:/Users/<USER>/ros2_jazzy/install/ament_cmake_export_targets:/Users/<USER>/ros2_jazzy/install/ament_cmake_export_link_flags:/Users/<USER>/ros2_jazzy/install/ament_cmake_export_interfaces:/Users/<USER>/ros2_jazzy/install/ament_cmake_export_libraries:/Users/<USER>/ros2_jazzy/install/ament_cmake_export_include_directories:/Users/<USER>/ros2_jazzy/install/ament_cmake_export_definitions:/Users/<USER>/ros2_jazzy/install/ament_cmake_core:/Users/<USER>/ros2_jazzy/install/ament_package:/Users/<USER>/ros2_jazzy/install/ament_mypy:/Users/<USER>/ros2_jazzy/install/ament_lint_cmake:/Users/<USER>/ros2_jazzy/install/ament_flake8:/Users/<USER>/ros2_jazzy/install/ament_copyright:/Users/<USER>/ros2_jazzy/install/ament_lint:/Users/<USER>/ros2_jazzy/install/ament_index_python:/Users/<USER>/ros2_jazzy/install/ament_index_cpp:/Users/<USER>/ros2_jazzy/install/ament_cpplint:/Users/<USER>/ros2_jazzy/install/ament_cppcheck:/Users/<USER>/ros2_jazzy/install/ament_clang_tidy:/Users/<USER>/ros2_jazzy/install/ament_clang_format:/Users/<USER>/Work/drill2/onboard/install/foxglove_bridge:/Users/<USER>/Work/drill2/onboard/install/rosx_introspection:/Users/<USER>/Work/drill2/onboard/install/maneuver_builder_cpp CMAKE_PREFIX_PATH=/Users/<USER>/Work/drill2/onboard/install/drill_msgs:/opt/homebrew/opt/qt@5/lib/cmake:/opt/homebrew/opt:/Users/<USER>/Work/drill2/onboard/install/can_msgs:/opt/homebrew/opt/qt@5/lib/cmake:/opt/homebrew/opt:/Users/<USER>/ros2_jazzy/install/rosbag2:/Users/<USER>/ros2_jazzy/install/rosbag2_storage_default_plugins:/Users/<USER>/ros2_jazzy/install/rosbag2_storage_mcap:/Users/<USER>/ros2_jazzy/install/rosbag2_compression_zstd:/Users/<USER>/ros2_jazzy/install/mcap_vendor:/Users/<USER>/ros2_jazzy/install/zstd_vendor:/Users/<USER>/ros2_jazzy/install/rviz_visual_testing_framework:/Users/<USER>/ros2_jazzy/install/rviz2:/Users/<USER>/ros2_jazzy/install/rviz_default_plugins:/Users/<USER>/ros2_jazzy/install/rviz_common:/Users/<USER>/ros2_jazzy/install/rosbag2_py:/Users/<USER>/ros2_jazzy/install/rosbag2_transport:/Users/<USER>/ros2_jazzy/install/rosbag2_storage_sqlite3:/Users/<USER>/ros2_jazzy/install/rosbag2_examples_cpp:/Users/<USER>/ros2_jazzy/install/rosbag2_compression:/Users/<USER>/ros2_jazzy/install/rosbag2_cpp:/Users/<USER>/ros2_jazzy/install/rosbag2_storage:/Users/<USER>/ros2_jazzy/install/image_common:/Users/<USER>/ros2_jazzy/install/camera_info_manager:/Users/<USER>/ros2_jazzy/install/camera_calibration_parsers:/Users/<USER>/ros2_jazzy/install/yaml_cpp_vendor:/Users/<USER>/ros2_jazzy/install/interactive_markers:/Users/<USER>/ros2_jazzy/install/common_interfaces:/Users/<USER>/ros2_jazzy/install/visualization_msgs:/Users/<USER>/ros2_jazzy/install/dummy_robot_bringup:/Users/<USER>/ros2_jazzy/install/robot_state_publisher:/Users/<USER>/ros2_jazzy/install/kdl_parser:/Users/<USER>/ros2_jazzy/install/urdf:/Users/<USER>/ros2_jazzy/install/urdfdom:/Users/<USER>/ros2_jazzy/install/urdf_parser_plugin:/Users/<USER>/ros2_jazzy/install/urdfdom_headers:/Users/<USER>/ros2_jazzy/install/turtlesim:/Users/<USER>/ros2_jazzy/install/trajectory_msgs:/Users/<USER>/ros2_jazzy/install/topic_statistics_demo:/Users/<USER>/ros2_jazzy/install/geometry2:/Users/<USER>/ros2_jazzy/install/tf2_sensor_msgs:/Users/<USER>/ros2_jazzy/install/test_tf2:/Users/<USER>/ros2_jazzy/install/tf2_kdl:/Users/<USER>/ros2_jazzy/install/tf2_geometry_msgs:/Users/<USER>/ros2_jazzy/install/tf2_eigen:/Users/<USER>/ros2_jazzy/install/tf2_bullet:/Users/<USER>/ros2_jazzy/install/tf2_ros:/Users/<USER>/ros2_jazzy/install/tf2_py:/Users/<USER>/ros2_jazzy/install/tf2_msgs:/Users/<USER>/ros2_jazzy/install/tf2_eigen_kdl:/Users/<USER>/ros2_jazzy/install/laser_geometry:/Users/<USER>/ros2_jazzy/install/tf2:/Users/<USER>/ros2_jazzy/install/test_tracetools:/Users/<USER>/ros2_jazzy/install/rosbag2_test_common:/Users/<USER>/ros2_jazzy/install/test_msgs:/Users/<USER>/ros2_jazzy/install/test_communication:/Users/<USER>/ros2_jazzy/install/stereo_msgs:/Users/<USER>/ros2_jazzy/install/std_srvs:/Users/<USER>/ros2_jazzy/install/shape_msgs:/Users/<USER>/ros2_jazzy/install/quality_of_service_demo_cpp:/Users/<USER>/ros2_jazzy/install/point_cloud_transport_py:/Users/<USER>/ros2_jazzy/install/point_cloud_transport:/Users/<USER>/ros2_jazzy/install/map_msgs:/Users/<USER>/ros2_jazzy/install/intra_process_demo:/Users/<USER>/ros2_jazzy/install/image_transport:/Users/<USER>/ros2_jazzy/install/image_tools:/Users/<USER>/ros2_jazzy/install/dummy_sensors:/Users/<USER>/ros2_jazzy/install/sensor_msgs:/Users/<USER>/ros2_jazzy/install/ros2cli_common_extensions:/Users/<USER>/ros2_jazzy/install/dummy_map_server:/Users/<USER>/ros2_jazzy/install/nav_msgs:/Users/<USER>/ros2_jazzy/install/message_filters:/Users/<USER>/ros2_jazzy/install/logging_demo:/Users/<USER>/ros2_jazzy/install/lifecycle:/Users/<USER>/ros2_jazzy/install/diagnostic_msgs:/Users/<USER>/ros2_jazzy/install/geometry_msgs:/Users/<USER>/ros2_jazzy/install/examples_rclcpp_wait_set:/Users/<USER>/ros2_jazzy/install/examples_rclcpp_multithreaded_executor:/Users/<USER>/ros2_jazzy/install/examples_rclcpp_minimal_subscriber:/Users/<USER>/ros2_jazzy/install/examples_rclcpp_minimal_publisher:/Users/<USER>/ros2_jazzy/install/examples_rclcpp_minimal_composition:/Users/<USER>/ros2_jazzy/install/examples_rclcpp_cbg_executor:/Users/<USER>/ros2_jazzy/install/demo_nodes_cpp_native:/Users/<USER>/ros2_jazzy/install/demo_nodes_cpp:/Users/<USER>/ros2_jazzy/install/composition:/Users/<USER>/ros2_jazzy/install/actionlib_msgs:/Users/<USER>/ros2_jazzy/install/std_msgs:/Users/<USER>/ros2_jazzy/install/ros2lifecycle_test_fixtures:/Users/<USER>/ros2_jazzy/install/rclcpp_lifecycle:/Users/<USER>/ros2_jazzy/install/action_tutorials_cpp:/Users/<USER>/ros2_jazzy/install/rclcpp_components:/Users/<USER>/ros2_jazzy/install/examples_rclcpp_minimal_action_server:/Users/<USER>/ros2_jazzy/install/examples_rclcpp_minimal_action_client:/Users/<USER>/ros2_jazzy/install/rclcpp_action:/Users/<USER>/ros2_jazzy/install/examples_rclcpp_minimal_timer:/Users/<USER>/ros2_jazzy/install/examples_rclcpp_minimal_service:/Users/<USER>/ros2_jazzy/install/examples_rclcpp_minimal_client:/Users/<USER>/ros2_jazzy/install/examples_rclcpp_async_client:/Users/<USER>/ros2_jazzy/install/rclcpp:/Users/<USER>/ros2_jazzy/install/libstatistics_collector:/Users/<USER>/ros2_jazzy/install/statistics_msgs:/Users/<USER>/ros2_jazzy/install/sros2_cmake:/Users/<USER>/ros2_jazzy/install/ros_testing:/Users/<USER>/ros2_jazzy/install/rclpy:/Users/<USER>/ros2_jazzy/install/rosgraph_msgs:/Users/<USER>/ros2_jazzy/install/rosbag2_interfaces:/Users/<USER>/ros2_jazzy/install/ros2cli_test_interfaces:/Users/<USER>/ros2_jazzy/install/rcl_lifecycle:/Users/<USER>/ros2_jazzy/install/rcl_action:/Users/<USER>/ros2_jazzy/install/rcl:/Users/<USER>/ros2_jazzy/install/rmw_implementation:/Users/<USER>/ros2_jazzy/install/rmw_fastrtps_dynamic_cpp:/Users/<USER>/ros2_jazzy/install/rmw_fastrtps_cpp:/Users/<USER>/ros2_jazzy/install/rmw_fastrtps_shared_cpp:/Users/<USER>/ros2_jazzy/install/rmw_cyclonedds_cpp:/Users/<USER>/ros2_jazzy/install/rmw_connextddsmicro:/Users/<USER>/ros2_jazzy/install/rmw_connextdds:/Users/<USER>/ros2_jazzy/install/rmw_connextdds_common:/Users/<USER>/ros2_jazzy/install/rmw_dds_common:/Users/<USER>/ros2_jazzy/install/composition_interfaces:/Users/<USER>/ros2_jazzy/install/rcl_interfaces:/Users/<USER>/ros2_jazzy/install/pendulum_msgs:/Users/<USER>/ros2_jazzy/install/lifecycle_msgs:/Users/<USER>/ros2_jazzy/install/example_interfaces:/Users/<USER>/ros2_jazzy/install/action_tutorials_interfaces:/Users/<USER>/ros2_jazzy/install/rosidl_default_runtime:/Users/<USER>/ros2_jazzy/install/rosidl_default_generators:/Users/<USER>/ros2_jazzy/install/action_msgs:/Users/<USER>/ros2_jazzy/install/unique_identifier_msgs:/Users/<USER>/ros2_jazzy/install/type_description_interfaces:/Users/<USER>/ros2_jazzy/install/service_msgs:/Users/<USER>/ros2_jazzy/install/builtin_interfaces:/Users/<USER>/ros2_jazzy/install/rosidl_core_runtime:/Users/<USER>/ros2_jazzy/install/rosidl_core_generators:/Users/<USER>/ros2_jazzy/install/rosidl_generator_py:/Users/<USER>/ros2_jazzy/install/ament_lint_common:/Users/<USER>/ros2_jazzy/install/ament_cmake_uncrustify:/Users/<USER>/ros2_jazzy/install/uncrustify_vendor:/Users/<USER>/ros2_jazzy/install/tracetools:/Users/<USER>/ros2_jazzy/install/pluginlib:/Users/<USER>/ros2_jazzy/install/tinyxml2_vendor:/Users/<USER>/ros2_jazzy/install/test_security:/Users/<USER>/ros2_jazzy/install/test_rmw_implementation:/Users/<USER>/ros2_jazzy/install/test_rclcpp:/Users/<USER>/ros2_jazzy/install/test_quality_of_service:/Users/<USER>/ros2_jazzy/install/test_launch_testing:/Users/<USER>/ros2_jazzy/install/test_interface_files:/Users/<USER>/ros2_jazzy/install/test_cli_remapping:/Users/<USER>/ros2_jazzy/install/test_cli:/Users/<USER>/ros2_jazzy/install/tango_icons_vendor:/Users/<USER>/ros2_jazzy/install/sqlite3_vendor:/Users/<USER>/ros2_jazzy/install/rcl_logging_spdlog:/Users/<USER>/ros2_jazzy/install/spdlog_vendor:/Users/<USER>/ros2_jazzy/install/shared_queues_vendor:/Users/<USER>/ros2_jazzy/install/rviz_rendering_tests:/Users/<USER>/ros2_jazzy/install/rviz_rendering:/Users/<USER>/ros2_jazzy/install/rviz_ogre_vendor:/Users/<USER>/ros2_jazzy/install/rviz_assimp_vendor:/Users/<USER>/ros2_jazzy/install/rti_connext_dds_cmake_module:/Users/<USER>/ros2_jazzy/install/lttngpy:/Users/<USER>/ros2_jazzy/install/rosidl_typesupport_tests:/Users/<USER>/ros2_jazzy/install/rosidl_typesupport_introspection_tests:/Users/<USER>/ros2_jazzy/install/rosidl_typesupport_cpp:/Users/<USER>/ros2_jazzy/install/rosidl_typesupport_introspection_cpp:/Users/<USER>/ros2_jazzy/install/rosidl_typesupport_c:/Users/<USER>/ros2_jazzy/install/rosidl_typesupport_introspection_c:/Users/<USER>/ros2_jazzy/install/rosidl_typesupport_fastrtps_c:/Users/<USER>/ros2_jazzy/install/rosidl_typesupport_fastrtps_cpp:/Users/<USER>/ros2_jazzy/install/rosidl_generator_cpp:/Users/<USER>/ros2_jazzy/install/rosidl_runtime_cpp:/Users/<USER>/ros2_jazzy/install/rosidl_dynamic_typesupport_fastrtps:/Users/<USER>/ros2_jazzy/install/rcl_yaml_param_parser:/Users/<USER>/ros2_jazzy/install/rmw:/Users/<USER>/ros2_jazzy/install/rosidl_dynamic_typesupport:/Users/<USER>/ros2_jazzy/install/rosidl_runtime_c:/Users/<USER>/ros2_jazzy/install/rosidl_generator_c:/Users/<USER>/ros2_jazzy/install/rosidl_typesupport_interface:/Users/<USER>/ros2_jazzy/install/rosidl_generator_dds_idl:/Users/<USER>/ros2_jazzy/install/rosidl_cmake:/Users/<USER>/ros2_jazzy/install/rosidl_generator_type_description:/Users/<USER>/ros2_jazzy/install/rosidl_parser:/Users/<USER>/ros2_jazzy/install/rosidl_generator_tests:/Users/<USER>/ros2_jazzy/install/rosidl_adapter:/Users/<USER>/ros2_jazzy/install/rosbag2_tests:/Users/<USER>/ros2_jazzy/install/rosbag2_test_msgdefs:/Users/<USER>/ros2_jazzy/install/ros_environment:/Users/<USER>/ros2_jazzy/install/rmw_implementation_cmake:/Users/<USER>/ros2_jazzy/install/resource_retriever:/Users/<USER>/ros2_jazzy/install/class_loader:/Users/<USER>/ros2_jazzy/install/rcpputils:/Users/<USER>/ros2_jazzy/install/rcl_logging_noop:/Users/<USER>/ros2_jazzy/install/rcl_logging_interface:/Users/<USER>/ros2_jazzy/install/rcutils:/Users/<USER>/ros2_jazzy/install/python_qt_binding:/Users/<USER>/ros2_jazzy/install/python_orocos_kdl_vendor:/Users/<USER>/ros2_jazzy/install/launch_testing_ament_cmake:/Users/<USER>/ros2_jazzy/install/python_cmake_module:/Users/<USER>/ros2_jazzy/install/pybind11_vendor:/Users/<USER>/ros2_jazzy/install/performance_test_fixture:/Users/<USER>/ros2_jazzy/install/osrf_testing_tools_cpp:/Users/<USER>/ros2_jazzy/install/orocos_kdl_vendor:/Users/<USER>/ros2_jazzy/install/mimick_vendor:/Users/<USER>/ros2_jazzy/install/libyaml_vendor:/Users/<USER>/ros2_jazzy/install/liblz4_vendor:/Users/<USER>/ros2_jazzy/install/libcurl_vendor:/Users/<USER>/ros2_jazzy/install/keyboard_handler:/Users/<USER>/ros2_jazzy/install/iceoryx_introspection:/Users/<USER>/ros2_jazzy/install/cyclonedds:/Users/<USER>/ros2_jazzy/install/iceoryx_posh:/Users/<USER>/ros2_jazzy/install/iceoryx_hoofs:/Users/<USER>/ros2_jazzy/install/iceoryx_binding_c:/Users/<USER>/ros2_jazzy/install/gz_math_vendor/opt/gz_math_vendor:/Users/<USER>/ros2_jazzy/install/gz_math_vendor:/Users/<USER>/ros2_jazzy/install/gz_utils_vendor/opt/gz_utils_vendor:/Users/<USER>/ros2_jazzy/install/gz_utils_vendor:/Users/<USER>/ros2_jazzy/install/gz_cmake_vendor/opt/gz_cmake_vendor:/Users/<USER>/ros2_jazzy/install/gz_cmake_vendor:/Users/<USER>/ros2_jazzy/install/ament_cmake_ros:/Users/<USER>/ros2_jazzy/install/ament_cmake_auto:/Users/<USER>/ros2_jazzy/install/ament_cmake_gmock:/Users/<USER>/ros2_jazzy/install/gmock_vendor:/Users/<USER>/ros2_jazzy/install/fastrtps:/Users/<USER>/ros2_jazzy/install/fastcdr:/Users/<USER>/ros2_jazzy/install/ament_cmake_gtest:/Users/<USER>/ros2_jazzy/install/gtest_vendor:/Users/<USER>/ros2_jazzy/install/ament_cmake_google_benchmark:/Users/<USER>/ros2_jazzy/install/google_benchmark_vendor:/Users/<USER>/ros2_jazzy/install/foonathan_memory_vendor:/Users/<USER>/ros2_jazzy/install/fastrtps_cmake_module:/Users/<USER>/ros2_jazzy/install/eigen3_cmake_module:/Users/<USER>/ros2_jazzy/install/console_bridge_vendor:/Users/<USER>/ros2_jazzy/install/ament_cmake_xmllint:/Users/<USER>/ros2_jazzy/install/ament_cmake_pyflakes:/Users/<USER>/ros2_jazzy/install/ament_cmake_pycodestyle:/Users/<USER>/ros2_jazzy/install/ament_cmake_pep257:/Users/<USER>/ros2_jazzy/install/ament_cmake_pclint:/Users/<USER>/ros2_jazzy/install/ament_lint_auto:/Users/<USER>/ros2_jazzy/install/ament_cmake:/Users/<USER>/ros2_jazzy/install/ament_cmake_version:/Users/<USER>/ros2_jazzy/install/ament_cmake_vendor_package:/Users/<USER>/ros2_jazzy/install/ament_cmake_pytest:/Users/<USER>/ros2_jazzy/install/ament_cmake_mypy:/Users/<USER>/ros2_jazzy/install/ament_cmake_lint_cmake:/Users/<USER>/ros2_jazzy/install/ament_cmake_flake8:/Users/<USER>/ros2_jazzy/install/ament_cmake_cpplint:/Users/<USER>/ros2_jazzy/install/ament_cmake_cppcheck:/Users/<USER>/ros2_jazzy/install/ament_cmake_copyright:/Users/<USER>/ros2_jazzy/install/ament_cmake_clang_tidy:/Users/<USER>/ros2_jazzy/install/ament_cmake_clang_format:/Users/<USER>/ros2_jazzy/install/ament_cmake_test:/Users/<USER>/ros2_jazzy/install/ament_cmake_target_dependencies:/Users/<USER>/ros2_jazzy/install/ament_cmake_python:/Users/<USER>/ros2_jazzy/install/ament_cmake_export_dependencies:/Users/<USER>/ros2_jazzy/install/ament_cmake_libraries:/Users/<USER>/ros2_jazzy/install/ament_cmake_include_directories:/Users/<USER>/ros2_jazzy/install/ament_cmake_gen_version_h:/Users/<USER>/ros2_jazzy/install/ament_cmake_export_targets:/Users/<USER>/ros2_jazzy/install/ament_cmake_export_link_flags:/Users/<USER>/ros2_jazzy/install/ament_cmake_export_interfaces:/Users/<USER>/ros2_jazzy/install/ament_cmake_export_libraries:/Users/<USER>/ros2_jazzy/install/ament_cmake_export_include_directories:/Users/<USER>/ros2_jazzy/install/ament_cmake_export_definitions:/Users/<USER>/ros2_jazzy/install/ament_cmake_core:/Users/<USER>/ros2_jazzy/install/ament_index_cpp:/Users/<USER>/Work/drill2/onboard/install/foxglove_bridge:/Users/<USER>/Work/drill2/onboard/install/rosx_introspection:/Users/<USER>/Work/drill2/onboard/install/maneuver_builder_cpp:/opt/homebrew/opt/qt@5/lib/cmake:/opt/homebrew/opt PYTHONPATH=/Users/<USER>/Work/drill2/onboard/build/tracks_regulator/prefix_override:/Users/<USER>/.ros2_venv/lib/python3.11/site-packages/colcon_core/task/python/colcon_distutils_commands:/Users/<USER>/Work/drill2/onboard/install/tracks_regulator/lib/python3.11/site-packages:/Users/<USER>/Work/drill2/onboard/build/base_node:/Users/<USER>/Work/drill2/onboard/install/base_node/lib/python3.11/site-packages:/Users/<USER>/Work/drill2/onboard/install/drill_msgs/lib/python3.11/site-packages:/Users/<USER>/Work/drill2/onboard/build/remote_connector:/Users/<USER>/Work/drill2/onboard/install/remote_connector/lib/python3.11/site-packages:/Users/<USER>/Work/drill2/onboard/build/tracks_regulator:/Users/<USER>/Work/drill2/onboard/install/tracks_regulator/lib/python3.11/site-packages:/Users/<USER>/Work/drill2/onboard/build/state_tracker:/Users/<USER>/Work/drill2/onboard/install/state_tracker/lib/python3.11/site-packages:/Users/<USER>/Work/drill2/onboard/build/rtk_connector:/Users/<USER>/Work/drill2/onboard/install/rtk_connector/lib/python3.11/site-packages:/Users/<USER>/Work/drill2/onboard/build/path_follower:/Users/<USER>/Work/drill2/onboard/install/path_follower/lib/python3.11/site-packages:/Users/<USER>/Work/drill2/onboard/build/params_server:/Users/<USER>/Work/drill2/onboard/install/params_server/lib/python3.11/site-packages:/Users/<USER>/Work/drill2/onboard/build/leveler:/Users/<USER>/Work/drill2/onboard/install/leveler/lib/python3.11/site-packages:/Users/<USER>/Work/drill2/onboard/build/launchpack:/Users/<USER>/Work/drill2/onboard/install/launchpack/lib/python3.11/site-packages:/Users/<USER>/Work/drill2/onboard/build/driller:/Users/<USER>/Work/drill2/onboard/install/driller/lib/python3.11/site-packages:/Users/<USER>/Work/drill2/onboard/build/depth_tracker:/Users/<USER>/Work/drill2/onboard/install/depth_tracker/lib/python3.11/site-packages:/Users/<USER>/Work/drill2/onboard/build/can_encoder:/Users/<USER>/Work/drill2/onboard/install/can_encoder/lib/python3.11/site-packages:/Users/<USER>/Work/drill2/onboard/build/can_decoder:/Users/<USER>/Work/drill2/onboard/install/can_decoder/lib/python3.11/site-packages:/Users/<USER>/Work/drill2/onboard/build/arm_controller:/Users/<USER>/Work/drill2/onboard/install/arm_controller/lib/python3.11/site-packages:/Users/<USER>/Work/drill2/onboard/install/can_msgs/lib/python3.11/site-packages:/Users/<USER>/ros2_jazzy/install/rosbag2_storage_mcap/lib/python3.11/site-packages:/Users/<USER>/ros2_jazzy/build/rosbag2_examples_py:/Users/<USER>/ros2_jazzy/install/rosbag2_examples_py/lib/python3.11/site-packages:/Users/<USER>/ros2_jazzy/build/launch_testing_examples:/Users/<USER>/ros2_jazzy/install/launch_testing_examples/lib/python3.11/site-packages:/Users/<USER>/ros2_jazzy/build/ros2bag:/Users/<USER>/ros2_jazzy/install/ros2bag/lib/python3.11/site-packages:/Users/<USER>/ros2_jazzy/install/rosbag2_py/lib/python3.11/site-packages:/Users/<USER>/ros2_jazzy/install/rosbag2_storage_sqlite3/lib/python3.11/site-packages:/Users/<USER>/ros2_jazzy/install/interactive_markers/lib/python3.11/site-packages:/Users/<USER>/ros2_jazzy/install/visualization_msgs/lib/python3.11/site-packages:/Users/<USER>/ros2_jazzy/install/turtlesim/lib/python3.11/site-packages:/Users/<USER>/ros2_jazzy/install/trajectory_msgs/lib/python3.11/site-packages:/Users/<USER>/ros2_jazzy/build/tracetools_test:/Users/<USER>/ros2_jazzy/install/tracetools_test/lib/python3.11/site-packages:/Users/<USER>/ros2_jazzy/build/tracetools_launch:/Users/<USER>/ros2_jazzy/install/tracetools_launch/lib/python3.11/site-packages:/Users/<USER>/ros2_jazzy/build/topic_monitor:/Users/<USER>/ros2_jazzy/install/topic_monitor/lib/python3.11/site-packages:/Users/<USER>/ros2_jazzy/build/tf2_tools:/Users/<USER>/ros2_jazzy/install/tf2_tools/lib/python3.11/site-packages:/Users/<USER>/ros2_jazzy/install/tf2_sensor_msgs/lib/python3.11/site-packages:/Users/<USER>/ros2_jazzy/install/tf2_kdl/lib/python3.11/site-packages:/Users/<USER>/ros2_jazzy/install/tf2_geometry_msgs/lib/python3.11/site-packages:/Users/<USER>/ros2_jazzy/build/examples_tf2_py:/Users/<USER>/ros2_jazzy/install/examples_tf2_py/lib/python3.11/site-packages:/Users/<USER>/ros2_jazzy/build/tf2_ros_py:/Users/<USER>/ros2_jazzy/install/tf2_ros_py/lib/python3.11/site-packages:/Users/<USER>/ros2_jazzy/install/tf2_py/lib/python3.11/site-packages:/Users/<USER>/ros2_jazzy/install/tf2_msgs/lib/python3.11/site-packages:/Users/<USER>/ros2_jazzy/install/laser_geometry/lib/python3.11/site-packages:/Users/<USER>/ros2_jazzy/install/rosbag2_test_common/lib/python3.11/site-packages:/Users/<USER>/ros2_jazzy/install/test_msgs/lib/python3.11/site-packages:/Users/<USER>/ros2_jazzy/install/stereo_msgs/lib/python3.11/site-packages:/Users/<USER>/ros2_jazzy/install/std_srvs/lib/python3.11/site-packages:/Users/<USER>/ros2_jazzy/install/shape_msgs/lib/python3.11/site-packages:/Users/<USER>/ros2_jazzy/build/examples_rclpy_pointcloud_publisher:/Users/<USER>/ros2_jazzy/install/examples_rclpy_pointcloud_publisher/lib/python3.11/site-packages:/Users/<USER>/ros2_jazzy/build/sensor_msgs_py:/Users/<USER>/ros2_jazzy/install/sensor_msgs_py/lib/python3.11/site-packages:/Users/<USER>/ros2_jazzy/build/quality_of_service_demo_py:/Users/<USER>/ros2_jazzy/install/quality_of_service_demo_py/lib/python3.11/site-packages:/Users/<USER>/ros2_jazzy/install/point_cloud_transport_py/lib/python3.11/site-packages:/Users/<USER>/ros2_jazzy/install/map_msgs/lib/python3.11/site-packages:/Users/<USER>/ros2_jazzy/build/camera_info_manager_py:/Users/<USER>/ros2_jazzy/install/camera_info_manager_py/lib/python3.11/site-packages:/Users/<USER>/ros2_jazzy/install/sensor_msgs/lib/python3.11/site-packages:/Users/<USER>/ros2_jazzy/build/ros2doctor:/Users/<USER>/ros2_jazzy/install/ros2doctor/lib/python3.11/site-packages:/Users/<USER>/ros2_jazzy/install/nav_msgs/lib/python3.11/site-packages:/Users/<USER>/ros2_jazzy/install/message_filters/lib/python3.11/site-packages:/Users/<USER>/ros2_jazzy/install/logging_demo/lib/python3.11/site-packages:/Users/<USER>/ros2_jazzy/build/lifecycle_py:/Users/<USER>/ros2_jazzy/install/lifecycle_py/lib/python3.11/site-packages:/Users/<USER>/ros2_jazzy/install/diagnostic_msgs/lib/python3.11/site-packages:/Users/<USER>/ros2_jazzy/install/geometry_msgs/lib/python3.11/site-packages:/Users/<USER>/ros2_jazzy/build/examples_rclpy_minimal_subscriber:/Users/<USER>/ros2_jazzy/install/examples_rclpy_minimal_subscriber/lib/python3.11/site-packages:/Users/<USER>/ros2_jazzy/build/examples_rclpy_minimal_service:/Users/<USER>/ros2_jazzy/install/examples_rclpy_minimal_service/lib/python3.11/site-packages:/Users/<USER>/ros2_jazzy/build/examples_rclpy_minimal_publisher:/Users/<USER>/ros2_jazzy/install/examples_rclpy_minimal_publisher/lib/python3.11/site-packages:/Users/<USER>/ros2_jazzy/build/examples_rclpy_minimal_client:/Users/<USER>/ros2_jazzy/install/examples_rclpy_minimal_client/lib/python3.11/site-packages:/Users/<USER>/ros2_jazzy/build/examples_rclpy_executors:/Users/<USER>/ros2_jazzy/install/examples_rclpy_executors/lib/python3.11/site-packages:/Users/<USER>/ros2_jazzy/build/demo_nodes_py:/Users/<USER>/ros2_jazzy/install/demo_nodes_py/lib/python3.11/site-packages:/Users/<USER>/ros2_jazzy/install/actionlib_msgs/lib/python3.11/site-packages:/Users/<USER>/ros2_jazzy/install/std_msgs/lib/python3.11/site-packages:/Users/<USER>/ros2_jazzy/build/ros2component:/Users/<USER>/ros2_jazzy/install/ros2component/lib/python3.11/site-packages:/Users/<USER>/ros2_jazzy/install/statistics_msgs/lib/python3.11/site-packages:/Users/<USER>/ros2_jazzy/build/sros2:/Users/<USER>/ros2_jazzy/install/sros2/lib/python3.11/site-packages:/Users/<USER>/ros2_jazzy/build/ros2trace:/Users/<USER>/ros2_jazzy/install/ros2trace/lib/python3.11/site-packages:/Users/<USER>/ros2_jazzy/build/ros2topic:/Users/<USER>/ros2_jazzy/install/ros2topic/lib/python3.11/site-packages:/Users/<USER>/ros2_jazzy/build/ros2test:/Users/<USER>/ros2_jazzy/install/ros2test/lib/python3.11/site-packages:/Users/<USER>/ros2_jazzy/build/ros2param:/Users/<USER>/ros2_jazzy/install/ros2param/lib/python3.11/site-packages:/Users/<USER>/ros2_jazzy/build/ros2lifecycle:/Users/<USER>/ros2_jazzy/install/ros2lifecycle/lib/python3.11/site-packages:/Users/<USER>/ros2_jazzy/build/ros2service:/Users/<USER>/ros2_jazzy/install/ros2service/lib/python3.11/site-packages:/Users/<USER>/ros2_jazzy/build/ros2run:/Users/<USER>/ros2_jazzy/install/ros2run/lib/python3.11/site-packages:/Users/<USER>/ros2_jazzy/build/ros2launch:/Users/<USER>/ros2_jazzy/install/ros2launch/lib/python3.11/site-packages:/Users/<USER>/ros2_jazzy/build/ros2pkg:/Users/<USER>/ros2_jazzy/install/ros2pkg/lib/python3.11/site-packages:/Users/<USER>/ros2_jazzy/build/ros2node:/Users/<USER>/ros2_jazzy/install/ros2node/lib/python3.11/site-packages:/Users/<USER>/ros2_jazzy/build/ros2multicast:/Users/<USER>/ros2_jazzy/install/ros2multicast/lib/python3.11/site-packages:/Users/<USER>/ros2_jazzy/build/ros2interface:/Users/<USER>/ros2_jazzy/install/ros2interface/lib/python3.11/site-packages:/Users/<USER>/ros2_jazzy/build/ros2action:/Users/<USER>/ros2_jazzy/install/ros2action/lib/python3.11/site-packages:/Users/<USER>/ros2_jazzy/build/ros2cli:/Users/<USER>/ros2_jazzy/install/ros2cli/lib/python3.11/site-packages:/Users/<USER>/ros2_jazzy/build/launch_testing_ros:/Users/<USER>/ros2_jazzy/install/launch_testing_ros/lib/python3.11/site-packages:/Users/<USER>/ros2_jazzy/build/launch_ros:/Users/<USER>/ros2_jazzy/install/launch_ros/lib/python3.11/site-packages:/Users/<USER>/ros2_jazzy/build/examples_rclpy_minimal_action_server:/Users/<USER>/ros2_jazzy/install/examples_rclpy_minimal_action_server/lib/python3.11/site-packages:/Users/<USER>/ros2_jazzy/build/examples_rclpy_minimal_action_client:/Users/<USER>/ros2_jazzy/install/examples_rclpy_minimal_action_client/lib/python3.11/site-packages:/Users/<USER>/ros2_jazzy/build/examples_rclpy_guard_conditions:/Users/<USER>/ros2_jazzy/install/examples_rclpy_guard_conditions/lib/python3.11/site-packages:/Users/<USER>/ros2_jazzy/build/action_tutorials_py:/Users/<USER>/ros2_jazzy/install/action_tutorials_py/lib/python3.11/site-packages:/Users/<USER>/ros2_jazzy/install/rclpy/lib/python3.11/site-packages:/Users/<USER>/ros2_jazzy/install/rosgraph_msgs/lib/python3.11/site-packages:/Users/<USER>/ros2_jazzy/install/rosbag2_interfaces/lib/python3.11/site-packages:/Users/<USER>/ros2_jazzy/install/ros2cli_test_interfaces/lib/python3.11/site-packages:/Users/<USER>/ros2_jazzy/install/rmw_dds_common/lib/python3.11/site-packages:/Users/<USER>/ros2_jazzy/install/composition_interfaces/lib/python3.11/site-packages:/Users/<USER>/ros2_jazzy/install/rcl_interfaces/lib/python3.11/site-packages:/Users/<USER>/ros2_jazzy/install/pendulum_msgs/lib/python3.11/site-packages:/Users/<USER>/ros2_jazzy/install/lifecycle_msgs/lib/python3.11/site-packages:/Users/<USER>/ros2_jazzy/install/example_interfaces/lib/python3.11/site-packages:/Users/<USER>/ros2_jazzy/install/action_tutorials_interfaces/lib/python3.11/site-packages:/Users/<USER>/ros2_jazzy/install/action_msgs/lib/python3.11/site-packages:/Users/<USER>/ros2_jazzy/install/unique_identifier_msgs/lib/python3.11/site-packages:/Users/<USER>/ros2_jazzy/install/type_description_interfaces/lib/python3.11/site-packages:/Users/<USER>/ros2_jazzy/install/service_msgs/lib/python3.11/site-packages:/Users/<USER>/ros2_jazzy/install/builtin_interfaces/lib/python3.11/site-packages:/Users/<USER>/ros2_jazzy/install/rosidl_generator_py/lib/python3.11/site-packages:/Users/<USER>/ros2_jazzy/build/ament_uncrustify:/Users/<USER>/ros2_jazzy/install/ament_uncrustify/lib/python3.11/site-packages:/Users/<USER>/ros2_jazzy/build/tracetools_trace:/Users/<USER>/ros2_jazzy/install/tracetools_trace/lib/python3.11/site-packages:/Users/<USER>/ros2_jazzy/build/tracetools_read:/Users/<USER>/ros2_jazzy/install/tracetools_read/lib/python3.11/site-packages:/Users/<USER>/ros2_jazzy/build/test_tracetools_launch:/Users/<USER>/ros2_jazzy/install/test_tracetools_launch/lib/python3.11/site-packages:/Users/<USER>/ros2_jazzy/build/test_ros2trace:/Users/<USER>/ros2_jazzy/install/test_ros2trace/lib/python3.11/site-packages:/Users/<USER>/ros2_jazzy/build/test_launch_ros:/Users/<USER>/ros2_jazzy/install/test_launch_ros/lib/python3.11/site-packages:/Users/<USER>/ros2_jazzy/install/lttngpy/lib/python3.11/site-packages:/Users/<USER>/ros2_jazzy/build/rpyutils:/Users/<USER>/ros2_jazzy/install/rpyutils/lib/python3.11/site-packages:/Users/<USER>/ros2_jazzy/install/rosidl_typesupport_cpp/lib/python3.11/site-packages:/Users/<USER>/ros2_jazzy/install/rosidl_typesupport_introspection_cpp/lib/python3.11/site-packages:/Users/<USER>/ros2_jazzy/install/rosidl_typesupport_c/lib/python3.11/site-packages:/Users/<USER>/ros2_jazzy/install/rosidl_typesupport_introspection_c/lib/python3.11/site-packages:/Users/<USER>/ros2_jazzy/install/rosidl_typesupport_fastrtps_c/lib/python3.11/site-packages:/Users/<USER>/ros2_jazzy/install/rosidl_typesupport_fastrtps_cpp/lib/python3.11/site-packages:/Users/<USER>/ros2_jazzy/install/rosidl_generator_cpp/lib/python3.11/site-packages:/Users/<USER>/ros2_jazzy/install/rosidl_generator_c/lib/python3.11/site-packages:/Users/<USER>/ros2_jazzy/build/rosidl_runtime_py:/Users/<USER>/ros2_jazzy/install/rosidl_runtime_py/lib/python3.11/site-packages:/Users/<USER>/ros2_jazzy/install/rosidl_generator_dds_idl/lib/python3.11/site-packages:/Users/<USER>/ros2_jazzy/install/rosidl_cmake/lib/python3.11/site-packages:/Users/<USER>/ros2_jazzy/build/rosidl_pycommon:/Users/<USER>/ros2_jazzy/install/rosidl_pycommon/lib/python3.11/site-packages:/Users/<USER>/ros2_jazzy/install/rosidl_generator_type_description/lib/python3.11/site-packages:/Users/<USER>/ros2_jazzy/install/rosidl_parser/lib/python3.11/site-packages:/Users/<USER>/ros2_jazzy/install/rosidl_adapter/lib/python3.11/site-packages:/Users/<USER>/ros2_jazzy/build/rosidl_cli:/Users/<USER>/ros2_jazzy/install/rosidl_cli/lib/python3.11/site-packages:/Users/<USER>/ros2_jazzy/install/rosbag2_test_msgdefs/lib/python3.11/site-packages:/Users/<USER>/ros2_jazzy/install/resource_retriever/lib/python3.11/site-packages:/Users/<USER>/ros2_jazzy/install/rcutils/lib/python3.11/site-packages:/Users/<USER>/ros2_jazzy/install/python_qt_binding/lib/python3.11/site-packages:/Users/<USER>/ros2_jazzy/install/python_orocos_kdl_vendor/lib/python3.11/dist-packages:/Users/<USER>/ros2_jazzy/build/launch_pytest:/Users/<USER>/ros2_jazzy/install/launch_pytest/lib/python3.11/site-packages:/Users/<USER>/ros2_jazzy/build/launch_testing:/Users/<USER>/ros2_jazzy/install/launch_testing/lib/python3.11/site-packages:/Users/<USER>/ros2_jazzy/build/launch_yaml:/Users/<USER>/ros2_jazzy/install/launch_yaml/lib/python3.11/site-packages:/Users/<USER>/ros2_jazzy/build/launch_xml:/Users/<USER>/ros2_jazzy/install/launch_xml/lib/python3.11/site-packages:/Users/<USER>/ros2_jazzy/build/launch:/Users/<USER>/ros2_jazzy/install/launch/lib/python3.11/site-packages:/Users/<USER>/ros2_jazzy/build/osrf_pycommon:/Users/<USER>/ros2_jazzy/install/osrf_pycommon/lib/python3.11/site-packages:/Users/<USER>/ros2_jazzy/install/ament_cmake_google_benchmark/lib/python3.11/site-packages:/Users/<USER>/ros2_jazzy/build/domain_coordinator:/Users/<USER>/ros2_jazzy/install/domain_coordinator/lib/python3.11/site-packages:/Users/<USER>/ros2_jazzy/build/ament_xmllint:/Users/<USER>/ros2_jazzy/install/ament_xmllint/lib/python3.11/site-packages:/Users/<USER>/ros2_jazzy/build/ament_pyflakes:/Users/<USER>/ros2_jazzy/install/ament_pyflakes/lib/python3.11/site-packages:/Users/<USER>/ros2_jazzy/build/ament_pycodestyle:/Users/<USER>/ros2_jazzy/install/ament_pycodestyle/lib/python3.11/site-packages:/Users/<USER>/ros2_jazzy/build/ament_pep257:/Users/<USER>/ros2_jazzy/install/ament_pep257/lib/python3.11/site-packages:/Users/<USER>/ros2_jazzy/build/ament_pclint:/Users/<USER>/ros2_jazzy/install/ament_pclint/lib/python3.11/site-packages:/Users/<USER>/ros2_jazzy/install/ament_cmake_test/lib/python3.11/site-packages:/Users/<USER>/ros2_jazzy/build/ament_package:/Users/<USER>/ros2_jazzy/install/ament_package/lib/python3.11/site-packages:/Users/<USER>/ros2_jazzy/build/ament_mypy:/Users/<USER>/ros2_jazzy/install/ament_mypy/lib/python3.11/site-packages:/Users/<USER>/ros2_jazzy/build/ament_lint_cmake:/Users/<USER>/ros2_jazzy/install/ament_lint_cmake/lib/python3.11/site-packages:/Users/<USER>/ros2_jazzy/build/ament_flake8:/Users/<USER>/ros2_jazzy/install/ament_flake8/lib/python3.11/site-packages:/Users/<USER>/ros2_jazzy/build/ament_copyright:/Users/<USER>/ros2_jazzy/install/ament_copyright/lib/python3.11/site-packages:/Users/<USER>/ros2_jazzy/build/ament_lint:/Users/<USER>/ros2_jazzy/install/ament_lint/lib/python3.11/site-packages:/Users/<USER>/ros2_jazzy/build/ament_index_python:/Users/<USER>/ros2_jazzy/install/ament_index_python/lib/python3.11/site-packages:/Users/<USER>/ros2_jazzy/build/ament_cpplint:/Users/<USER>/ros2_jazzy/install/ament_cpplint/lib/python3.11/site-packages:/Users/<USER>/ros2_jazzy/build/ament_cppcheck:/Users/<USER>/ros2_jazzy/install/ament_cppcheck/lib/python3.11/site-packages:/Users/<USER>/ros2_jazzy/build/ament_clang_tidy:/Users/<USER>/ros2_jazzy/install/ament_clang_tidy/lib/python3.11/site-packages:/Users/<USER>/ros2_jazzy/build/ament_clang_format:/Users/<USER>/ros2_jazzy/install/ament_clang_format/lib/python3.11/site-packages:/Users/<USER>/Work/drill2/onboard/build/drill_regulator:/Users/<USER>/Work/drill2/onboard/install/drill_regulator/lib/python3.11/site-packages SHLVL=2 VIRTUAL_ENV_PROMPT=(.ros2_venv) _=/usr/bin/env /Users/<USER>/.ros2_venv/bin/python3 -W ignore:setup.py install is deprecated -W ignore:easy_install command is deprecated setup.py develop --editable --build-directory /Users/<USER>/Work/drill2/onboard/build/tracks_regulator/build --no-deps symlink_data
Invoked command in '/Users/<USER>/Work/drill2/onboard/build/tracks_regulator' returned '0': AMENT_PREFIX_PATH=/Users/<USER>/Work/drill2/onboard/install/base_node:/Users/<USER>/Work/drill2/onboard/install/remote_connector:/Users/<USER>/Work/drill2/onboard/install/tracks_regulator:/Users/<USER>/Work/drill2/onboard/install/state_tracker:/Users/<USER>/Work/drill2/onboard/install/rtk_connector:/Users/<USER>/Work/drill2/onboard/install/path_follower:/Users/<USER>/Work/drill2/onboard/install/params_server:/Users/<USER>/Work/drill2/onboard/install/leveler:/Users/<USER>/Work/drill2/onboard/install/launchpack:/Users/<USER>/Work/drill2/onboard/install/driller:/Users/<USER>/Work/drill2/onboard/install/depth_tracker:/Users/<USER>/Work/drill2/onboard/install/can_encoder:/Users/<USER>/Work/drill2/onboard/install/can_decoder:/Users/<USER>/Work/drill2/onboard/install/arm_controller:/Users/<USER>/Work/drill2/onboard/install/drill_msgs:/Users/<USER>/Work/drill2/onboard/install/can_msgs:/Users/<USER>/ros2_jazzy/install/rosbag2:/Users/<USER>/ros2_jazzy/install/rosbag2_storage_default_plugins:/Users/<USER>/ros2_jazzy/install/rosbag2_storage_mcap:/Users/<USER>/ros2_jazzy/install/rosbag2_compression_zstd:/Users/<USER>/ros2_jazzy/install/mcap_vendor:/Users/<USER>/ros2_jazzy/install/zstd_vendor:/Users/<USER>/ros2_jazzy/install/rviz_visual_testing_framework:/Users/<USER>/ros2_jazzy/install/rviz2:/Users/<USER>/ros2_jazzy/install/rviz_default_plugins:/Users/<USER>/ros2_jazzy/install/rviz_common:/Users/<USER>/ros2_jazzy/install/rosbag2_examples_py:/Users/<USER>/ros2_jazzy/install/launch_testing_examples:/Users/<USER>/ros2_jazzy/install/ros2bag:/Users/<USER>/ros2_jazzy/install/rosbag2_py:/Users/<USER>/ros2_jazzy/install/rosbag2_transport:/Users/<USER>/ros2_jazzy/install/rosbag2_storage_sqlite3:/Users/<USER>/ros2_jazzy/install/rosbag2_examples_cpp:/Users/<USER>/ros2_jazzy/install/rosbag2_compression:/Users/<USER>/ros2_jazzy/install/rosbag2_cpp:/Users/<USER>/ros2_jazzy/install/rosbag2_storage:/Users/<USER>/ros2_jazzy/install/image_common:/Users/<USER>/ros2_jazzy/install/camera_info_manager:/Users/<USER>/ros2_jazzy/install/camera_calibration_parsers:/Users/<USER>/ros2_jazzy/install/yaml_cpp_vendor:/Users/<USER>/ros2_jazzy/install/interactive_markers:/Users/<USER>/ros2_jazzy/install/common_interfaces:/Users/<USER>/ros2_jazzy/install/visualization_msgs:/Users/<USER>/ros2_jazzy/install/dummy_robot_bringup:/Users/<USER>/ros2_jazzy/install/robot_state_publisher:/Users/<USER>/ros2_jazzy/install/kdl_parser:/Users/<USER>/ros2_jazzy/install/urdf:/Users/<USER>/ros2_jazzy/install/urdf_parser_plugin:/Users/<USER>/ros2_jazzy/install/turtlesim:/Users/<USER>/ros2_jazzy/install/trajectory_msgs:/Users/<USER>/ros2_jazzy/install/tracetools_test:/Users/<USER>/ros2_jazzy/install/tracetools_launch:/Users/<USER>/ros2_jazzy/install/topic_statistics_demo:/Users/<USER>/ros2_jazzy/install/topic_monitor:/Users/<USER>/ros2_jazzy/install/geometry2:/Users/<USER>/ros2_jazzy/install/tf2_tools:/Users/<USER>/ros2_jazzy/install/tf2_sensor_msgs:/Users/<USER>/ros2_jazzy/install/test_tf2:/Users/<USER>/ros2_jazzy/install/tf2_kdl:/Users/<USER>/ros2_jazzy/install/tf2_geometry_msgs:/Users/<USER>/ros2_jazzy/install/examples_tf2_py:/Users/<USER>/ros2_jazzy/install/tf2_ros_py:/Users/<USER>/ros2_jazzy/install/tf2_eigen:/Users/<USER>/ros2_jazzy/install/tf2_bullet:/Users/<USER>/ros2_jazzy/install/tf2_ros:/Users/<USER>/ros2_jazzy/install/tf2_py:/Users/<USER>/ros2_jazzy/install/tf2_msgs:/Users/<USER>/ros2_jazzy/install/tf2_eigen_kdl:/Users/<USER>/ros2_jazzy/install/laser_geometry:/Users/<USER>/ros2_jazzy/install/tf2:/Users/<USER>/ros2_jazzy/install/test_tracetools:/Users/<USER>/ros2_jazzy/install/rosbag2_test_common:/Users/<USER>/ros2_jazzy/install/test_msgs:/Users/<USER>/ros2_jazzy/install/test_communication:/Users/<USER>/ros2_jazzy/install/stereo_msgs:/Users/<USER>/ros2_jazzy/install/std_srvs:/Users/<USER>/ros2_jazzy/install/shape_msgs:/Users/<USER>/ros2_jazzy/install/examples_rclpy_pointcloud_publisher:/Users/<USER>/ros2_jazzy/install/sensor_msgs_py:/Users/<USER>/ros2_jazzy/install/quality_of_service_demo_py:/Users/<USER>/ros2_jazzy/install/quality_of_service_demo_cpp:/Users/<USER>/ros2_jazzy/install/point_cloud_transport_py:/Users/<USER>/ros2_jazzy/install/point_cloud_transport:/Users/<USER>/ros2_jazzy/install/map_msgs:/Users/<USER>/ros2_jazzy/install/intra_process_demo:/Users/<USER>/ros2_jazzy/install/image_transport:/Users/<USER>/ros2_jazzy/install/image_tools:/Users/<USER>/ros2_jazzy/install/dummy_sensors:/Users/<USER>/ros2_jazzy/install/camera_info_manager_py:/Users/<USER>/ros2_jazzy/install/sensor_msgs:/Users/<USER>/ros2_jazzy/install/ros2cli_common_extensions:/Users/<USER>/ros2_jazzy/install/ros2doctor:/Users/<USER>/ros2_jazzy/install/dummy_map_server:/Users/<USER>/ros2_jazzy/install/nav_msgs:/Users/<USER>/ros2_jazzy/install/message_filters:/Users/<USER>/ros2_jazzy/install/logging_demo:/Users/<USER>/ros2_jazzy/install/lifecycle_py:/Users/<USER>/ros2_jazzy/install/lifecycle:/Users/<USER>/ros2_jazzy/install/diagnostic_msgs:/Users/<USER>/ros2_jazzy/install/geometry_msgs:/Users/<USER>/ros2_jazzy/install/examples_rclpy_minimal_subscriber:/Users/<USER>/ros2_jazzy/install/examples_rclpy_minimal_service:/Users/<USER>/ros2_jazzy/install/examples_rclpy_minimal_publisher:/Users/<USER>/ros2_jazzy/install/examples_rclpy_minimal_client:/Users/<USER>/ros2_jazzy/install/examples_rclpy_executors:/Users/<USER>/ros2_jazzy/install/examples_rclcpp_wait_set:/Users/<USER>/ros2_jazzy/install/examples_rclcpp_multithreaded_executor:/Users/<USER>/ros2_jazzy/install/examples_rclcpp_minimal_subscriber:/Users/<USER>/ros2_jazzy/install/examples_rclcpp_minimal_publisher:/Users/<USER>/ros2_jazzy/install/examples_rclcpp_minimal_composition:/Users/<USER>/ros2_jazzy/install/examples_rclcpp_cbg_executor:/Users/<USER>/ros2_jazzy/install/demo_nodes_py:/Users/<USER>/ros2_jazzy/install/demo_nodes_cpp_native:/Users/<USER>/ros2_jazzy/install/demo_nodes_cpp:/Users/<USER>/ros2_jazzy/install/composition:/Users/<USER>/ros2_jazzy/install/actionlib_msgs:/Users/<USER>/ros2_jazzy/install/std_msgs:/Users/<USER>/ros2_jazzy/install/ros2lifecycle_test_fixtures:/Users/<USER>/ros2_jazzy/install/ros2component:/Users/<USER>/ros2_jazzy/install/rclcpp_lifecycle:/Users/<USER>/ros2_jazzy/install/action_tutorials_cpp:/Users/<USER>/ros2_jazzy/install/rclcpp_components:/Users/<USER>/ros2_jazzy/install/examples_rclcpp_minimal_action_server:/Users/<USER>/ros2_jazzy/install/examples_rclcpp_minimal_action_client:/Users/<USER>/ros2_jazzy/install/rclcpp_action:/Users/<USER>/ros2_jazzy/install/examples_rclcpp_minimal_timer:/Users/<USER>/ros2_jazzy/install/examples_rclcpp_minimal_service:/Users/<USER>/ros2_jazzy/install/examples_rclcpp_minimal_client:/Users/<USER>/ros2_jazzy/install/examples_rclcpp_async_client:/Users/<USER>/ros2_jazzy/install/rclcpp:/Users/<USER>/ros2_jazzy/install/libstatistics_collector:/Users/<USER>/ros2_jazzy/install/statistics_msgs:/Users/<USER>/ros2_jazzy/install/sros2_cmake:/Users/<USER>/ros2_jazzy/install/sros2:/Users/<USER>/ros2_jazzy/install/ros_testing:/Users/<USER>/ros2_jazzy/install/ros2trace:/Users/<USER>/ros2_jazzy/install/ros2topic:/Users/<USER>/ros2_jazzy/install/ros2test:/Users/<USER>/ros2_jazzy/install/ros2param:/Users/<USER>/ros2_jazzy/install/ros2lifecycle:/Users/<USER>/ros2_jazzy/install/ros2service:/Users/<USER>/ros2_jazzy/install/ros2run:/Users/<USER>/ros2_jazzy/install/ros2launch:/Users/<USER>/ros2_jazzy/install/ros2pkg:/Users/<USER>/ros2_jazzy/install/ros2node:/Users/<USER>/ros2_jazzy/install/ros2multicast:/Users/<USER>/ros2_jazzy/install/ros2interface:/Users/<USER>/ros2_jazzy/install/ros2action:/Users/<USER>/ros2_jazzy/install/ros2cli:/Users/<USER>/ros2_jazzy/install/launch_testing_ros:/Users/<USER>/ros2_jazzy/install/launch_ros:/Users/<USER>/ros2_jazzy/install/examples_rclpy_minimal_action_server:/Users/<USER>/ros2_jazzy/install/examples_rclpy_minimal_action_client:/Users/<USER>/ros2_jazzy/install/examples_rclpy_guard_conditions:/Users/<USER>/ros2_jazzy/install/action_tutorials_py:/Users/<USER>/ros2_jazzy/install/rclpy:/Users/<USER>/ros2_jazzy/install/rosgraph_msgs:/Users/<USER>/ros2_jazzy/install/rosbag2_interfaces:/Users/<USER>/ros2_jazzy/install/ros2cli_test_interfaces:/Users/<USER>/ros2_jazzy/install/rcl_lifecycle:/Users/<USER>/ros2_jazzy/install/rcl_action:/Users/<USER>/ros2_jazzy/install/rcl:/Users/<USER>/ros2_jazzy/install/rmw_implementation:/Users/<USER>/ros2_jazzy/install/rmw_fastrtps_dynamic_cpp:/Users/<USER>/ros2_jazzy/install/rmw_fastrtps_cpp:/Users/<USER>/ros2_jazzy/install/rmw_fastrtps_shared_cpp:/Users/<USER>/ros2_jazzy/install/rmw_cyclonedds_cpp:/Users/<USER>/ros2_jazzy/install/rmw_connextddsmicro:/Users/<USER>/ros2_jazzy/install/rmw_connextdds:/Users/<USER>/ros2_jazzy/install/rmw_connextdds_common:/Users/<USER>/ros2_jazzy/install/rmw_dds_common:/Users/<USER>/ros2_jazzy/install/composition_interfaces:/Users/<USER>/ros2_jazzy/install/rcl_interfaces:/Users/<USER>/ros2_jazzy/install/pendulum_msgs:/Users/<USER>/ros2_jazzy/install/lifecycle_msgs:/Users/<USER>/ros2_jazzy/install/example_interfaces:/Users/<USER>/ros2_jazzy/install/action_tutorials_interfaces:/Users/<USER>/ros2_jazzy/install/rosidl_default_runtime:/Users/<USER>/ros2_jazzy/install/rosidl_default_generators:/Users/<USER>/ros2_jazzy/install/action_msgs:/Users/<USER>/ros2_jazzy/install/unique_identifier_msgs:/Users/<USER>/ros2_jazzy/install/type_description_interfaces:/Users/<USER>/ros2_jazzy/install/service_msgs:/Users/<USER>/ros2_jazzy/install/builtin_interfaces:/Users/<USER>/ros2_jazzy/install/rosidl_core_runtime:/Users/<USER>/ros2_jazzy/install/rosidl_core_generators:/Users/<USER>/ros2_jazzy/install/rosidl_generator_py:/Users/<USER>/ros2_jazzy/install/ament_lint_common:/Users/<USER>/ros2_jazzy/install/ament_cmake_uncrustify:/Users/<USER>/ros2_jazzy/install/ament_uncrustify:/Users/<USER>/ros2_jazzy/install/uncrustify_vendor:/Users/<USER>/ros2_jazzy/install/tracetools_trace:/Users/<USER>/ros2_jazzy/install/tracetools_read:/Users/<USER>/ros2_jazzy/install/tracetools:/Users/<USER>/ros2_jazzy/install/pluginlib:/Users/<USER>/ros2_jazzy/install/tinyxml2_vendor:/Users/<USER>/ros2_jazzy/install/test_tracetools_launch:/Users/<USER>/ros2_jazzy/install/test_security:/Users/<USER>/ros2_jazzy/install/test_ros2trace:/Users/<USER>/ros2_jazzy/install/test_rmw_implementation:/Users/<USER>/ros2_jazzy/install/test_rclcpp:/Users/<USER>/ros2_jazzy/install/test_quality_of_service:/Users/<USER>/ros2_jazzy/install/test_launch_testing:/Users/<USER>/ros2_jazzy/install/test_launch_ros:/Users/<USER>/ros2_jazzy/install/test_interface_files:/Users/<USER>/ros2_jazzy/install/test_cli_remapping:/Users/<USER>/ros2_jazzy/install/test_cli:/Users/<USER>/ros2_jazzy/install/tango_icons_vendor:/Users/<USER>/ros2_jazzy/install/sqlite3_vendor:/Users/<USER>/ros2_jazzy/install/rcl_logging_spdlog:/Users/<USER>/ros2_jazzy/install/spdlog_vendor:/Users/<USER>/ros2_jazzy/install/shared_queues_vendor:/Users/<USER>/ros2_jazzy/install/rviz_rendering_tests:/Users/<USER>/ros2_jazzy/install/rviz_rendering:/Users/<USER>/ros2_jazzy/install/rviz_ogre_vendor:/Users/<USER>/ros2_jazzy/install/rviz_assimp_vendor:/Users/<USER>/ros2_jazzy/install/rti_connext_dds_cmake_module:/Users/<USER>/ros2_jazzy/install/lttngpy:/Users/<USER>/ros2_jazzy/install/rpyutils:/Users/<USER>/ros2_jazzy/install/rosidl_typesupport_tests:/Users/<USER>/ros2_jazzy/install/rosidl_typesupport_introspection_tests:/Users/<USER>/ros2_jazzy/install/rosidl_typesupport_cpp:/Users/<USER>/ros2_jazzy/install/rosidl_typesupport_introspection_cpp:/Users/<USER>/ros2_jazzy/install/rosidl_typesupport_c:/Users/<USER>/ros2_jazzy/install/rosidl_typesupport_introspection_c:/Users/<USER>/ros2_jazzy/install/rosidl_typesupport_fastrtps_c:/Users/<USER>/ros2_jazzy/install/rosidl_typesupport_fastrtps_cpp:/Users/<USER>/ros2_jazzy/install/rosidl_generator_cpp:/Users/<USER>/ros2_jazzy/install/rosidl_runtime_cpp:/Users/<USER>/ros2_jazzy/install/rosidl_dynamic_typesupport_fastrtps:/Users/<USER>/ros2_jazzy/install/rcl_yaml_param_parser:/Users/<USER>/ros2_jazzy/install/rmw:/Users/<USER>/ros2_jazzy/install/rosidl_dynamic_typesupport:/Users/<USER>/ros2_jazzy/install/rosidl_runtime_c:/Users/<USER>/ros2_jazzy/install/rosidl_generator_c:/Users/<USER>/ros2_jazzy/install/rosidl_typesupport_interface:/Users/<USER>/ros2_jazzy/install/rosidl_runtime_py:/Users/<USER>/ros2_jazzy/install/rosidl_generator_dds_idl:/Users/<USER>/ros2_jazzy/install/rosidl_cmake:/Users/<USER>/ros2_jazzy/install/rosidl_pycommon:/Users/<USER>/ros2_jazzy/install/rosidl_generator_type_description:/Users/<USER>/ros2_jazzy/install/rosidl_parser:/Users/<USER>/ros2_jazzy/install/rosidl_generator_tests:/Users/<USER>/ros2_jazzy/install/rosidl_adapter:/Users/<USER>/ros2_jazzy/install/rosidl_cli:/Users/<USER>/ros2_jazzy/install/rosbag2_tests:/Users/<USER>/ros2_jazzy/install/rosbag2_test_msgdefs:/Users/<USER>/ros2_jazzy/install/ros_environment:/Users/<USER>/ros2_jazzy/install/rmw_implementation_cmake:/Users/<USER>/ros2_jazzy/install/resource_retriever:/Users/<USER>/ros2_jazzy/install/class_loader:/Users/<USER>/ros2_jazzy/install/rcpputils:/Users/<USER>/ros2_jazzy/install/rcl_logging_noop:/Users/<USER>/ros2_jazzy/install/rcl_logging_interface:/Users/<USER>/ros2_jazzy/install/rcutils:/Users/<USER>/ros2_jazzy/install/python_qt_binding:/Users/<USER>/ros2_jazzy/install/python_orocos_kdl_vendor:/Users/<USER>/ros2_jazzy/install/launch_testing_ament_cmake:/Users/<USER>/ros2_jazzy/install/python_cmake_module:/Users/<USER>/ros2_jazzy/install/pybind11_vendor:/Users/<USER>/ros2_jazzy/install/performance_test_fixture:/Users/<USER>/ros2_jazzy/install/launch_pytest:/Users/<USER>/ros2_jazzy/install/launch_testing:/Users/<USER>/ros2_jazzy/install/launch_yaml:/Users/<USER>/ros2_jazzy/install/launch_xml:/Users/<USER>/ros2_jazzy/install/launch:/Users/<USER>/ros2_jazzy/install/osrf_pycommon:/Users/<USER>/ros2_jazzy/install/orocos_kdl_vendor:/Users/<USER>/ros2_jazzy/install/mimick_vendor:/Users/<USER>/ros2_jazzy/install/libyaml_vendor:/Users/<USER>/ros2_jazzy/install/liblz4_vendor:/Users/<USER>/ros2_jazzy/install/libcurl_vendor:/Users/<USER>/ros2_jazzy/install/keyboard_handler:/Users/<USER>/ros2_jazzy/install/gz_math_vendor:/Users/<USER>/ros2_jazzy/install/gz_utils_vendor:/Users/<USER>/ros2_jazzy/install/gz_cmake_vendor:/Users/<USER>/ros2_jazzy/install/ament_cmake_ros:/Users/<USER>/ros2_jazzy/install/ament_cmake_auto:/Users/<USER>/ros2_jazzy/install/ament_cmake_gmock:/Users/<USER>/ros2_jazzy/install/ament_cmake_gtest:/Users/<USER>/ros2_jazzy/install/ament_cmake_google_benchmark:/Users/<USER>/ros2_jazzy/install/fastrtps_cmake_module:/Users/<USER>/ros2_jazzy/install/eigen3_cmake_module:/Users/<USER>/ros2_jazzy/install/domain_coordinator:/Users/<USER>/ros2_jazzy/install/console_bridge_vendor:/Users/<USER>/ros2_jazzy/install/ament_cmake_xmllint:/Users/<USER>/ros2_jazzy/install/ament_xmllint:/Users/<USER>/ros2_jazzy/install/ament_cmake_pyflakes:/Users/<USER>/ros2_jazzy/install/ament_pyflakes:/Users/<USER>/ros2_jazzy/install/ament_cmake_pycodestyle:/Users/<USER>/ros2_jazzy/install/ament_pycodestyle:/Users/<USER>/ros2_jazzy/install/ament_cmake_pep257:/Users/<USER>/ros2_jazzy/install/ament_pep257:/Users/<USER>/ros2_jazzy/install/ament_cmake_pclint:/Users/<USER>/ros2_jazzy/install/ament_pclint:/Users/<USER>/ros2_jazzy/install/ament_lint_auto:/Users/<USER>/ros2_jazzy/install/ament_cmake:/Users/<USER>/ros2_jazzy/install/ament_cmake_version:/Users/<USER>/ros2_jazzy/install/ament_cmake_vendor_package:/Users/<USER>/ros2_jazzy/install/ament_cmake_pytest:/Users/<USER>/ros2_jazzy/install/ament_cmake_mypy:/Users/<USER>/ros2_jazzy/install/ament_cmake_lint_cmake:/Users/<USER>/ros2_jazzy/install/ament_cmake_flake8:/Users/<USER>/ros2_jazzy/install/ament_cmake_cpplint:/Users/<USER>/ros2_jazzy/install/ament_cmake_cppcheck:/Users/<USER>/ros2_jazzy/install/ament_cmake_copyright:/Users/<USER>/ros2_jazzy/install/ament_cmake_clang_tidy:/Users/<USER>/ros2_jazzy/install/ament_cmake_clang_format:/Users/<USER>/ros2_jazzy/install/ament_cmake_test:/Users/<USER>/ros2_jazzy/install/ament_cmake_target_dependencies:/Users/<USER>/ros2_jazzy/install/ament_cmake_python:/Users/<USER>/ros2_jazzy/install/ament_cmake_export_dependencies:/Users/<USER>/ros2_jazzy/install/ament_cmake_libraries:/Users/<USER>/ros2_jazzy/install/ament_cmake_include_directories:/Users/<USER>/ros2_jazzy/install/ament_cmake_gen_version_h:/Users/<USER>/ros2_jazzy/install/ament_cmake_export_targets:/Users/<USER>/ros2_jazzy/install/ament_cmake_export_link_flags:/Users/<USER>/ros2_jazzy/install/ament_cmake_export_interfaces:/Users/<USER>/ros2_jazzy/install/ament_cmake_export_libraries:/Users/<USER>/ros2_jazzy/install/ament_cmake_export_include_directories:/Users/<USER>/ros2_jazzy/install/ament_cmake_export_definitions:/Users/<USER>/ros2_jazzy/install/ament_cmake_core:/Users/<USER>/ros2_jazzy/install/ament_package:/Users/<USER>/ros2_jazzy/install/ament_mypy:/Users/<USER>/ros2_jazzy/install/ament_lint_cmake:/Users/<USER>/ros2_jazzy/install/ament_flake8:/Users/<USER>/ros2_jazzy/install/ament_copyright:/Users/<USER>/ros2_jazzy/install/ament_lint:/Users/<USER>/ros2_jazzy/install/ament_index_python:/Users/<USER>/ros2_jazzy/install/ament_index_cpp:/Users/<USER>/ros2_jazzy/install/ament_cpplint:/Users/<USER>/ros2_jazzy/install/ament_cppcheck:/Users/<USER>/ros2_jazzy/install/ament_clang_tidy:/Users/<USER>/ros2_jazzy/install/ament_clang_format:/Users/<USER>/Work/drill2/onboard/install/foxglove_bridge:/Users/<USER>/Work/drill2/onboard/install/rosx_introspection:/Users/<USER>/Work/drill2/onboard/install/maneuver_builder_cpp CMAKE_PREFIX_PATH=/Users/<USER>/Work/drill2/onboard/install/drill_msgs:/opt/homebrew/opt/qt@5/lib/cmake:/opt/homebrew/opt:/Users/<USER>/Work/drill2/onboard/install/can_msgs:/opt/homebrew/opt/qt@5/lib/cmake:/opt/homebrew/opt:/Users/<USER>/ros2_jazzy/install/rosbag2:/Users/<USER>/ros2_jazzy/install/rosbag2_storage_default_plugins:/Users/<USER>/ros2_jazzy/install/rosbag2_storage_mcap:/Users/<USER>/ros2_jazzy/install/rosbag2_compression_zstd:/Users/<USER>/ros2_jazzy/install/mcap_vendor:/Users/<USER>/ros2_jazzy/install/zstd_vendor:/Users/<USER>/ros2_jazzy/install/rviz_visual_testing_framework:/Users/<USER>/ros2_jazzy/install/rviz2:/Users/<USER>/ros2_jazzy/install/rviz_default_plugins:/Users/<USER>/ros2_jazzy/install/rviz_common:/Users/<USER>/ros2_jazzy/install/rosbag2_py:/Users/<USER>/ros2_jazzy/install/rosbag2_transport:/Users/<USER>/ros2_jazzy/install/rosbag2_storage_sqlite3:/Users/<USER>/ros2_jazzy/install/rosbag2_examples_cpp:/Users/<USER>/ros2_jazzy/install/rosbag2_compression:/Users/<USER>/ros2_jazzy/install/rosbag2_cpp:/Users/<USER>/ros2_jazzy/install/rosbag2_storage:/Users/<USER>/ros2_jazzy/install/image_common:/Users/<USER>/ros2_jazzy/install/camera_info_manager:/Users/<USER>/ros2_jazzy/install/camera_calibration_parsers:/Users/<USER>/ros2_jazzy/install/yaml_cpp_vendor:/Users/<USER>/ros2_jazzy/install/interactive_markers:/Users/<USER>/ros2_jazzy/install/common_interfaces:/Users/<USER>/ros2_jazzy/install/visualization_msgs:/Users/<USER>/ros2_jazzy/install/dummy_robot_bringup:/Users/<USER>/ros2_jazzy/install/robot_state_publisher:/Users/<USER>/ros2_jazzy/install/kdl_parser:/Users/<USER>/ros2_jazzy/install/urdf:/Users/<USER>/ros2_jazzy/install/urdfdom:/Users/<USER>/ros2_jazzy/install/urdf_parser_plugin:/Users/<USER>/ros2_jazzy/install/urdfdom_headers:/Users/<USER>/ros2_jazzy/install/turtlesim:/Users/<USER>/ros2_jazzy/install/trajectory_msgs:/Users/<USER>/ros2_jazzy/install/topic_statistics_demo:/Users/<USER>/ros2_jazzy/install/geometry2:/Users/<USER>/ros2_jazzy/install/tf2_sensor_msgs:/Users/<USER>/ros2_jazzy/install/test_tf2:/Users/<USER>/ros2_jazzy/install/tf2_kdl:/Users/<USER>/ros2_jazzy/install/tf2_geometry_msgs:/Users/<USER>/ros2_jazzy/install/tf2_eigen:/Users/<USER>/ros2_jazzy/install/tf2_bullet:/Users/<USER>/ros2_jazzy/install/tf2_ros:/Users/<USER>/ros2_jazzy/install/tf2_py:/Users/<USER>/ros2_jazzy/install/tf2_msgs:/Users/<USER>/ros2_jazzy/install/tf2_eigen_kdl:/Users/<USER>/ros2_jazzy/install/laser_geometry:/Users/<USER>/ros2_jazzy/install/tf2:/Users/<USER>/ros2_jazzy/install/test_tracetools:/Users/<USER>/ros2_jazzy/install/rosbag2_test_common:/Users/<USER>/ros2_jazzy/install/test_msgs:/Users/<USER>/ros2_jazzy/install/test_communication:/Users/<USER>/ros2_jazzy/install/stereo_msgs:/Users/<USER>/ros2_jazzy/install/std_srvs:/Users/<USER>/ros2_jazzy/install/shape_msgs:/Users/<USER>/ros2_jazzy/install/quality_of_service_demo_cpp:/Users/<USER>/ros2_jazzy/install/point_cloud_transport_py:/Users/<USER>/ros2_jazzy/install/point_cloud_transport:/Users/<USER>/ros2_jazzy/install/map_msgs:/Users/<USER>/ros2_jazzy/install/intra_process_demo:/Users/<USER>/ros2_jazzy/install/image_transport:/Users/<USER>/ros2_jazzy/install/image_tools:/Users/<USER>/ros2_jazzy/install/dummy_sensors:/Users/<USER>/ros2_jazzy/install/sensor_msgs:/Users/<USER>/ros2_jazzy/install/ros2cli_common_extensions:/Users/<USER>/ros2_jazzy/install/dummy_map_server:/Users/<USER>/ros2_jazzy/install/nav_msgs:/Users/<USER>/ros2_jazzy/install/message_filters:/Users/<USER>/ros2_jazzy/install/logging_demo:/Users/<USER>/ros2_jazzy/install/lifecycle:/Users/<USER>/ros2_jazzy/install/diagnostic_msgs:/Users/<USER>/ros2_jazzy/install/geometry_msgs:/Users/<USER>/ros2_jazzy/install/examples_rclcpp_wait_set:/Users/<USER>/ros2_jazzy/install/examples_rclcpp_multithreaded_executor:/Users/<USER>/ros2_jazzy/install/examples_rclcpp_minimal_subscriber:/Users/<USER>/ros2_jazzy/install/examples_rclcpp_minimal_publisher:/Users/<USER>/ros2_jazzy/install/examples_rclcpp_minimal_composition:/Users/<USER>/ros2_jazzy/install/examples_rclcpp_cbg_executor:/Users/<USER>/ros2_jazzy/install/demo_nodes_cpp_native:/Users/<USER>/ros2_jazzy/install/demo_nodes_cpp:/Users/<USER>/ros2_jazzy/install/composition:/Users/<USER>/ros2_jazzy/install/actionlib_msgs:/Users/<USER>/ros2_jazzy/install/std_msgs:/Users/<USER>/ros2_jazzy/install/ros2lifecycle_test_fixtures:/Users/<USER>/ros2_jazzy/install/rclcpp_lifecycle:/Users/<USER>/ros2_jazzy/install/action_tutorials_cpp:/Users/<USER>/ros2_jazzy/install/rclcpp_components:/Users/<USER>/ros2_jazzy/install/examples_rclcpp_minimal_action_server:/Users/<USER>/ros2_jazzy/install/examples_rclcpp_minimal_action_client:/Users/<USER>/ros2_jazzy/install/rclcpp_action:/Users/<USER>/ros2_jazzy/install/examples_rclcpp_minimal_timer:/Users/<USER>/ros2_jazzy/install/examples_rclcpp_minimal_service:/Users/<USER>/ros2_jazzy/install/examples_rclcpp_minimal_client:/Users/<USER>/ros2_jazzy/install/examples_rclcpp_async_client:/Users/<USER>/ros2_jazzy/install/rclcpp:/Users/<USER>/ros2_jazzy/install/libstatistics_collector:/Users/<USER>/ros2_jazzy/install/statistics_msgs:/Users/<USER>/ros2_jazzy/install/sros2_cmake:/Users/<USER>/ros2_jazzy/install/ros_testing:/Users/<USER>/ros2_jazzy/install/rclpy:/Users/<USER>/ros2_jazzy/install/rosgraph_msgs:/Users/<USER>/ros2_jazzy/install/rosbag2_interfaces:/Users/<USER>/ros2_jazzy/install/ros2cli_test_interfaces:/Users/<USER>/ros2_jazzy/install/rcl_lifecycle:/Users/<USER>/ros2_jazzy/install/rcl_action:/Users/<USER>/ros2_jazzy/install/rcl:/Users/<USER>/ros2_jazzy/install/rmw_implementation:/Users/<USER>/ros2_jazzy/install/rmw_fastrtps_dynamic_cpp:/Users/<USER>/ros2_jazzy/install/rmw_fastrtps_cpp:/Users/<USER>/ros2_jazzy/install/rmw_fastrtps_shared_cpp:/Users/<USER>/ros2_jazzy/install/rmw_cyclonedds_cpp:/Users/<USER>/ros2_jazzy/install/rmw_connextddsmicro:/Users/<USER>/ros2_jazzy/install/rmw_connextdds:/Users/<USER>/ros2_jazzy/install/rmw_connextdds_common:/Users/<USER>/ros2_jazzy/install/rmw_dds_common:/Users/<USER>/ros2_jazzy/install/composition_interfaces:/Users/<USER>/ros2_jazzy/install/rcl_interfaces:/Users/<USER>/ros2_jazzy/install/pendulum_msgs:/Users/<USER>/ros2_jazzy/install/lifecycle_msgs:/Users/<USER>/ros2_jazzy/install/example_interfaces:/Users/<USER>/ros2_jazzy/install/action_tutorials_interfaces:/Users/<USER>/ros2_jazzy/install/rosidl_default_runtime:/Users/<USER>/ros2_jazzy/install/rosidl_default_generators:/Users/<USER>/ros2_jazzy/install/action_msgs:/Users/<USER>/ros2_jazzy/install/unique_identifier_msgs:/Users/<USER>/ros2_jazzy/install/type_description_interfaces:/Users/<USER>/ros2_jazzy/install/service_msgs:/Users/<USER>/ros2_jazzy/install/builtin_interfaces:/Users/<USER>/ros2_jazzy/install/rosidl_core_runtime:/Users/<USER>/ros2_jazzy/install/rosidl_core_generators:/Users/<USER>/ros2_jazzy/install/rosidl_generator_py:/Users/<USER>/ros2_jazzy/install/ament_lint_common:/Users/<USER>/ros2_jazzy/install/ament_cmake_uncrustify:/Users/<USER>/ros2_jazzy/install/uncrustify_vendor:/Users/<USER>/ros2_jazzy/install/tracetools:/Users/<USER>/ros2_jazzy/install/pluginlib:/Users/<USER>/ros2_jazzy/install/tinyxml2_vendor:/Users/<USER>/ros2_jazzy/install/test_security:/Users/<USER>/ros2_jazzy/install/test_rmw_implementation:/Users/<USER>/ros2_jazzy/install/test_rclcpp:/Users/<USER>/ros2_jazzy/install/test_quality_of_service:/Users/<USER>/ros2_jazzy/install/test_launch_testing:/Users/<USER>/ros2_jazzy/install/test_interface_files:/Users/<USER>/ros2_jazzy/install/test_cli_remapping:/Users/<USER>/ros2_jazzy/install/test_cli:/Users/<USER>/ros2_jazzy/install/tango_icons_vendor:/Users/<USER>/ros2_jazzy/install/sqlite3_vendor:/Users/<USER>/ros2_jazzy/install/rcl_logging_spdlog:/Users/<USER>/ros2_jazzy/install/spdlog_vendor:/Users/<USER>/ros2_jazzy/install/shared_queues_vendor:/Users/<USER>/ros2_jazzy/install/rviz_rendering_tests:/Users/<USER>/ros2_jazzy/install/rviz_rendering:/Users/<USER>/ros2_jazzy/install/rviz_ogre_vendor:/Users/<USER>/ros2_jazzy/install/rviz_assimp_vendor:/Users/<USER>/ros2_jazzy/install/rti_connext_dds_cmake_module:/Users/<USER>/ros2_jazzy/install/lttngpy:/Users/<USER>/ros2_jazzy/install/rosidl_typesupport_tests:/Users/<USER>/ros2_jazzy/install/rosidl_typesupport_introspection_tests:/Users/<USER>/ros2_jazzy/install/rosidl_typesupport_cpp:/Users/<USER>/ros2_jazzy/install/rosidl_typesupport_introspection_cpp:/Users/<USER>/ros2_jazzy/install/rosidl_typesupport_c:/Users/<USER>/ros2_jazzy/install/rosidl_typesupport_introspection_c:/Users/<USER>/ros2_jazzy/install/rosidl_typesupport_fastrtps_c:/Users/<USER>/ros2_jazzy/install/rosidl_typesupport_fastrtps_cpp:/Users/<USER>/ros2_jazzy/install/rosidl_generator_cpp:/Users/<USER>/ros2_jazzy/install/rosidl_runtime_cpp:/Users/<USER>/ros2_jazzy/install/rosidl_dynamic_typesupport_fastrtps:/Users/<USER>/ros2_jazzy/install/rcl_yaml_param_parser:/Users/<USER>/ros2_jazzy/install/rmw:/Users/<USER>/ros2_jazzy/install/rosidl_dynamic_typesupport:/Users/<USER>/ros2_jazzy/install/rosidl_runtime_c:/Users/<USER>/ros2_jazzy/install/rosidl_generator_c:/Users/<USER>/ros2_jazzy/install/rosidl_typesupport_interface:/Users/<USER>/ros2_jazzy/install/rosidl_generator_dds_idl:/Users/<USER>/ros2_jazzy/install/rosidl_cmake:/Users/<USER>/ros2_jazzy/install/rosidl_generator_type_description:/Users/<USER>/ros2_jazzy/install/rosidl_parser:/Users/<USER>/ros2_jazzy/install/rosidl_generator_tests:/Users/<USER>/ros2_jazzy/install/rosidl_adapter:/Users/<USER>/ros2_jazzy/install/rosbag2_tests:/Users/<USER>/ros2_jazzy/install/rosbag2_test_msgdefs:/Users/<USER>/ros2_jazzy/install/ros_environment:/Users/<USER>/ros2_jazzy/install/rmw_implementation_cmake:/Users/<USER>/ros2_jazzy/install/resource_retriever:/Users/<USER>/ros2_jazzy/install/class_loader:/Users/<USER>/ros2_jazzy/install/rcpputils:/Users/<USER>/ros2_jazzy/install/rcl_logging_noop:/Users/<USER>/ros2_jazzy/install/rcl_logging_interface:/Users/<USER>/ros2_jazzy/install/rcutils:/Users/<USER>/ros2_jazzy/install/python_qt_binding:/Users/<USER>/ros2_jazzy/install/python_orocos_kdl_vendor:/Users/<USER>/ros2_jazzy/install/launch_testing_ament_cmake:/Users/<USER>/ros2_jazzy/install/python_cmake_module:/Users/<USER>/ros2_jazzy/install/pybind11_vendor:/Users/<USER>/ros2_jazzy/install/performance_test_fixture:/Users/<USER>/ros2_jazzy/install/osrf_testing_tools_cpp:/Users/<USER>/ros2_jazzy/install/orocos_kdl_vendor:/Users/<USER>/ros2_jazzy/install/mimick_vendor:/Users/<USER>/ros2_jazzy/install/libyaml_vendor:/Users/<USER>/ros2_jazzy/install/liblz4_vendor:/Users/<USER>/ros2_jazzy/install/libcurl_vendor:/Users/<USER>/ros2_jazzy/install/keyboard_handler:/Users/<USER>/ros2_jazzy/install/iceoryx_introspection:/Users/<USER>/ros2_jazzy/install/cyclonedds:/Users/<USER>/ros2_jazzy/install/iceoryx_posh:/Users/<USER>/ros2_jazzy/install/iceoryx_hoofs:/Users/<USER>/ros2_jazzy/install/iceoryx_binding_c:/Users/<USER>/ros2_jazzy/install/gz_math_vendor/opt/gz_math_vendor:/Users/<USER>/ros2_jazzy/install/gz_math_vendor:/Users/<USER>/ros2_jazzy/install/gz_utils_vendor/opt/gz_utils_vendor:/Users/<USER>/ros2_jazzy/install/gz_utils_vendor:/Users/<USER>/ros2_jazzy/install/gz_cmake_vendor/opt/gz_cmake_vendor:/Users/<USER>/ros2_jazzy/install/gz_cmake_vendor:/Users/<USER>/ros2_jazzy/install/ament_cmake_ros:/Users/<USER>/ros2_jazzy/install/ament_cmake_auto:/Users/<USER>/ros2_jazzy/install/ament_cmake_gmock:/Users/<USER>/ros2_jazzy/install/gmock_vendor:/Users/<USER>/ros2_jazzy/install/fastrtps:/Users/<USER>/ros2_jazzy/install/fastcdr:/Users/<USER>/ros2_jazzy/install/ament_cmake_gtest:/Users/<USER>/ros2_jazzy/install/gtest_vendor:/Users/<USER>/ros2_jazzy/install/ament_cmake_google_benchmark:/Users/<USER>/ros2_jazzy/install/google_benchmark_vendor:/Users/<USER>/ros2_jazzy/install/foonathan_memory_vendor:/Users/<USER>/ros2_jazzy/install/fastrtps_cmake_module:/Users/<USER>/ros2_jazzy/install/eigen3_cmake_module:/Users/<USER>/ros2_jazzy/install/console_bridge_vendor:/Users/<USER>/ros2_jazzy/install/ament_cmake_xmllint:/Users/<USER>/ros2_jazzy/install/ament_cmake_pyflakes:/Users/<USER>/ros2_jazzy/install/ament_cmake_pycodestyle:/Users/<USER>/ros2_jazzy/install/ament_cmake_pep257:/Users/<USER>/ros2_jazzy/install/ament_cmake_pclint:/Users/<USER>/ros2_jazzy/install/ament_lint_auto:/Users/<USER>/ros2_jazzy/install/ament_cmake:/Users/<USER>/ros2_jazzy/install/ament_cmake_version:/Users/<USER>/ros2_jazzy/install/ament_cmake_vendor_package:/Users/<USER>/ros2_jazzy/install/ament_cmake_pytest:/Users/<USER>/ros2_jazzy/install/ament_cmake_mypy:/Users/<USER>/ros2_jazzy/install/ament_cmake_lint_cmake:/Users/<USER>/ros2_jazzy/install/ament_cmake_flake8:/Users/<USER>/ros2_jazzy/install/ament_cmake_cpplint:/Users/<USER>/ros2_jazzy/install/ament_cmake_cppcheck:/Users/<USER>/ros2_jazzy/install/ament_cmake_copyright:/Users/<USER>/ros2_jazzy/install/ament_cmake_clang_tidy:/Users/<USER>/ros2_jazzy/install/ament_cmake_clang_format:/Users/<USER>/ros2_jazzy/install/ament_cmake_test:/Users/<USER>/ros2_jazzy/install/ament_cmake_target_dependencies:/Users/<USER>/ros2_jazzy/install/ament_cmake_python:/Users/<USER>/ros2_jazzy/install/ament_cmake_export_dependencies:/Users/<USER>/ros2_jazzy/install/ament_cmake_libraries:/Users/<USER>/ros2_jazzy/install/ament_cmake_include_directories:/Users/<USER>/ros2_jazzy/install/ament_cmake_gen_version_h:/Users/<USER>/ros2_jazzy/install/ament_cmake_export_targets:/Users/<USER>/ros2_jazzy/install/ament_cmake_export_link_flags:/Users/<USER>/ros2_jazzy/install/ament_cmake_export_interfaces:/Users/<USER>/ros2_jazzy/install/ament_cmake_export_libraries:/Users/<USER>/ros2_jazzy/install/ament_cmake_export_include_directories:/Users/<USER>/ros2_jazzy/install/ament_cmake_export_definitions:/Users/<USER>/ros2_jazzy/install/ament_cmake_core:/Users/<USER>/ros2_jazzy/install/ament_index_cpp:/Users/<USER>/Work/drill2/onboard/install/foxglove_bridge:/Users/<USER>/Work/drill2/onboard/install/rosx_introspection:/Users/<USER>/Work/drill2/onboard/install/maneuver_builder_cpp:/opt/homebrew/opt/qt@5/lib/cmake:/opt/homebrew/opt PYTHONPATH=/Users/<USER>/Work/drill2/onboard/build/tracks_regulator/prefix_override:/Users/<USER>/.ros2_venv/lib/python3.11/site-packages/colcon_core/task/python/colcon_distutils_commands:/Users/<USER>/Work/drill2/onboard/install/tracks_regulator/lib/python3.11/site-packages:/Users/<USER>/Work/drill2/onboard/build/base_node:/Users/<USER>/Work/drill2/onboard/install/base_node/lib/python3.11/site-packages:/Users/<USER>/Work/drill2/onboard/install/drill_msgs/lib/python3.11/site-packages:/Users/<USER>/Work/drill2/onboard/build/remote_connector:/Users/<USER>/Work/drill2/onboard/install/remote_connector/lib/python3.11/site-packages:/Users/<USER>/Work/drill2/onboard/build/tracks_regulator:/Users/<USER>/Work/drill2/onboard/install/tracks_regulator/lib/python3.11/site-packages:/Users/<USER>/Work/drill2/onboard/build/state_tracker:/Users/<USER>/Work/drill2/onboard/install/state_tracker/lib/python3.11/site-packages:/Users/<USER>/Work/drill2/onboard/build/rtk_connector:/Users/<USER>/Work/drill2/onboard/install/rtk_connector/lib/python3.11/site-packages:/Users/<USER>/Work/drill2/onboard/build/path_follower:/Users/<USER>/Work/drill2/onboard/install/path_follower/lib/python3.11/site-packages:/Users/<USER>/Work/drill2/onboard/build/params_server:/Users/<USER>/Work/drill2/onboard/install/params_server/lib/python3.11/site-packages:/Users/<USER>/Work/drill2/onboard/build/leveler:/Users/<USER>/Work/drill2/onboard/install/leveler/lib/python3.11/site-packages:/Users/<USER>/Work/drill2/onboard/build/launchpack:/Users/<USER>/Work/drill2/onboard/install/launchpack/lib/python3.11/site-packages:/Users/<USER>/Work/drill2/onboard/build/driller:/Users/<USER>/Work/drill2/onboard/install/driller/lib/python3.11/site-packages:/Users/<USER>/Work/drill2/onboard/build/depth_tracker:/Users/<USER>/Work/drill2/onboard/install/depth_tracker/lib/python3.11/site-packages:/Users/<USER>/Work/drill2/onboard/build/can_encoder:/Users/<USER>/Work/drill2/onboard/install/can_encoder/lib/python3.11/site-packages:/Users/<USER>/Work/drill2/onboard/build/can_decoder:/Users/<USER>/Work/drill2/onboard/install/can_decoder/lib/python3.11/site-packages:/Users/<USER>/Work/drill2/onboard/build/arm_controller:/Users/<USER>/Work/drill2/onboard/install/arm_controller/lib/python3.11/site-packages:/Users/<USER>/Work/drill2/onboard/install/can_msgs/lib/python3.11/site-packages:/Users/<USER>/ros2_jazzy/install/rosbag2_storage_mcap/lib/python3.11/site-packages:/Users/<USER>/ros2_jazzy/build/rosbag2_examples_py:/Users/<USER>/ros2_jazzy/install/rosbag2_examples_py/lib/python3.11/site-packages:/Users/<USER>/ros2_jazzy/build/launch_testing_examples:/Users/<USER>/ros2_jazzy/install/launch_testing_examples/lib/python3.11/site-packages:/Users/<USER>/ros2_jazzy/build/ros2bag:/Users/<USER>/ros2_jazzy/install/ros2bag/lib/python3.11/site-packages:/Users/<USER>/ros2_jazzy/install/rosbag2_py/lib/python3.11/site-packages:/Users/<USER>/ros2_jazzy/install/rosbag2_storage_sqlite3/lib/python3.11/site-packages:/Users/<USER>/ros2_jazzy/install/interactive_markers/lib/python3.11/site-packages:/Users/<USER>/ros2_jazzy/install/visualization_msgs/lib/python3.11/site-packages:/Users/<USER>/ros2_jazzy/install/turtlesim/lib/python3.11/site-packages:/Users/<USER>/ros2_jazzy/install/trajectory_msgs/lib/python3.11/site-packages:/Users/<USER>/ros2_jazzy/build/tracetools_test:/Users/<USER>/ros2_jazzy/install/tracetools_test/lib/python3.11/site-packages:/Users/<USER>/ros2_jazzy/build/tracetools_launch:/Users/<USER>/ros2_jazzy/install/tracetools_launch/lib/python3.11/site-packages:/Users/<USER>/ros2_jazzy/build/topic_monitor:/Users/<USER>/ros2_jazzy/install/topic_monitor/lib/python3.11/site-packages:/Users/<USER>/ros2_jazzy/build/tf2_tools:/Users/<USER>/ros2_jazzy/install/tf2_tools/lib/python3.11/site-packages:/Users/<USER>/ros2_jazzy/install/tf2_sensor_msgs/lib/python3.11/site-packages:/Users/<USER>/ros2_jazzy/install/tf2_kdl/lib/python3.11/site-packages:/Users/<USER>/ros2_jazzy/install/tf2_geometry_msgs/lib/python3.11/site-packages:/Users/<USER>/ros2_jazzy/build/examples_tf2_py:/Users/<USER>/ros2_jazzy/install/examples_tf2_py/lib/python3.11/site-packages:/Users/<USER>/ros2_jazzy/build/tf2_ros_py:/Users/<USER>/ros2_jazzy/install/tf2_ros_py/lib/python3.11/site-packages:/Users/<USER>/ros2_jazzy/install/tf2_py/lib/python3.11/site-packages:/Users/<USER>/ros2_jazzy/install/tf2_msgs/lib/python3.11/site-packages:/Users/<USER>/ros2_jazzy/install/laser_geometry/lib/python3.11/site-packages:/Users/<USER>/ros2_jazzy/install/rosbag2_test_common/lib/python3.11/site-packages:/Users/<USER>/ros2_jazzy/install/test_msgs/lib/python3.11/site-packages:/Users/<USER>/ros2_jazzy/install/stereo_msgs/lib/python3.11/site-packages:/Users/<USER>/ros2_jazzy/install/std_srvs/lib/python3.11/site-packages:/Users/<USER>/ros2_jazzy/install/shape_msgs/lib/python3.11/site-packages:/Users/<USER>/ros2_jazzy/build/examples_rclpy_pointcloud_publisher:/Users/<USER>/ros2_jazzy/install/examples_rclpy_pointcloud_publisher/lib/python3.11/site-packages:/Users/<USER>/ros2_jazzy/build/sensor_msgs_py:/Users/<USER>/ros2_jazzy/install/sensor_msgs_py/lib/python3.11/site-packages:/Users/<USER>/ros2_jazzy/build/quality_of_service_demo_py:/Users/<USER>/ros2_jazzy/install/quality_of_service_demo_py/lib/python3.11/site-packages:/Users/<USER>/ros2_jazzy/install/point_cloud_transport_py/lib/python3.11/site-packages:/Users/<USER>/ros2_jazzy/install/map_msgs/lib/python3.11/site-packages:/Users/<USER>/ros2_jazzy/build/camera_info_manager_py:/Users/<USER>/ros2_jazzy/install/camera_info_manager_py/lib/python3.11/site-packages:/Users/<USER>/ros2_jazzy/install/sensor_msgs/lib/python3.11/site-packages:/Users/<USER>/ros2_jazzy/build/ros2doctor:/Users/<USER>/ros2_jazzy/install/ros2doctor/lib/python3.11/site-packages:/Users/<USER>/ros2_jazzy/install/nav_msgs/lib/python3.11/site-packages:/Users/<USER>/ros2_jazzy/install/message_filters/lib/python3.11/site-packages:/Users/<USER>/ros2_jazzy/install/logging_demo/lib/python3.11/site-packages:/Users/<USER>/ros2_jazzy/build/lifecycle_py:/Users/<USER>/ros2_jazzy/install/lifecycle_py/lib/python3.11/site-packages:/Users/<USER>/ros2_jazzy/install/diagnostic_msgs/lib/python3.11/site-packages:/Users/<USER>/ros2_jazzy/install/geometry_msgs/lib/python3.11/site-packages:/Users/<USER>/ros2_jazzy/build/examples_rclpy_minimal_subscriber:/Users/<USER>/ros2_jazzy/install/examples_rclpy_minimal_subscriber/lib/python3.11/site-packages:/Users/<USER>/ros2_jazzy/build/examples_rclpy_minimal_service:/Users/<USER>/ros2_jazzy/install/examples_rclpy_minimal_service/lib/python3.11/site-packages:/Users/<USER>/ros2_jazzy/build/examples_rclpy_minimal_publisher:/Users/<USER>/ros2_jazzy/install/examples_rclpy_minimal_publisher/lib/python3.11/site-packages:/Users/<USER>/ros2_jazzy/build/examples_rclpy_minimal_client:/Users/<USER>/ros2_jazzy/install/examples_rclpy_minimal_client/lib/python3.11/site-packages:/Users/<USER>/ros2_jazzy/build/examples_rclpy_executors:/Users/<USER>/ros2_jazzy/install/examples_rclpy_executors/lib/python3.11/site-packages:/Users/<USER>/ros2_jazzy/build/demo_nodes_py:/Users/<USER>/ros2_jazzy/install/demo_nodes_py/lib/python3.11/site-packages:/Users/<USER>/ros2_jazzy/install/actionlib_msgs/lib/python3.11/site-packages:/Users/<USER>/ros2_jazzy/install/std_msgs/lib/python3.11/site-packages:/Users/<USER>/ros2_jazzy/build/ros2component:/Users/<USER>/ros2_jazzy/install/ros2component/lib/python3.11/site-packages:/Users/<USER>/ros2_jazzy/install/statistics_msgs/lib/python3.11/site-packages:/Users/<USER>/ros2_jazzy/build/sros2:/Users/<USER>/ros2_jazzy/install/sros2/lib/python3.11/site-packages:/Users/<USER>/ros2_jazzy/build/ros2trace:/Users/<USER>/ros2_jazzy/install/ros2trace/lib/python3.11/site-packages:/Users/<USER>/ros2_jazzy/build/ros2topic:/Users/<USER>/ros2_jazzy/install/ros2topic/lib/python3.11/site-packages:/Users/<USER>/ros2_jazzy/build/ros2test:/Users/<USER>/ros2_jazzy/install/ros2test/lib/python3.11/site-packages:/Users/<USER>/ros2_jazzy/build/ros2param:/Users/<USER>/ros2_jazzy/install/ros2param/lib/python3.11/site-packages:/Users/<USER>/ros2_jazzy/build/ros2lifecycle:/Users/<USER>/ros2_jazzy/install/ros2lifecycle/lib/python3.11/site-packages:/Users/<USER>/ros2_jazzy/build/ros2service:/Users/<USER>/ros2_jazzy/install/ros2service/lib/python3.11/site-packages:/Users/<USER>/ros2_jazzy/build/ros2run:/Users/<USER>/ros2_jazzy/install/ros2run/lib/python3.11/site-packages:/Users/<USER>/ros2_jazzy/build/ros2launch:/Users/<USER>/ros2_jazzy/install/ros2launch/lib/python3.11/site-packages:/Users/<USER>/ros2_jazzy/build/ros2pkg:/Users/<USER>/ros2_jazzy/install/ros2pkg/lib/python3.11/site-packages:/Users/<USER>/ros2_jazzy/build/ros2node:/Users/<USER>/ros2_jazzy/install/ros2node/lib/python3.11/site-packages:/Users/<USER>/ros2_jazzy/build/ros2multicast:/Users/<USER>/ros2_jazzy/install/ros2multicast/lib/python3.11/site-packages:/Users/<USER>/ros2_jazzy/build/ros2interface:/Users/<USER>/ros2_jazzy/install/ros2interface/lib/python3.11/site-packages:/Users/<USER>/ros2_jazzy/build/ros2action:/Users/<USER>/ros2_jazzy/install/ros2action/lib/python3.11/site-packages:/Users/<USER>/ros2_jazzy/build/ros2cli:/Users/<USER>/ros2_jazzy/install/ros2cli/lib/python3.11/site-packages:/Users/<USER>/ros2_jazzy/build/launch_testing_ros:/Users/<USER>/ros2_jazzy/install/launch_testing_ros/lib/python3.11/site-packages:/Users/<USER>/ros2_jazzy/build/launch_ros:/Users/<USER>/ros2_jazzy/install/launch_ros/lib/python3.11/site-packages:/Users/<USER>/ros2_jazzy/build/examples_rclpy_minimal_action_server:/Users/<USER>/ros2_jazzy/install/examples_rclpy_minimal_action_server/lib/python3.11/site-packages:/Users/<USER>/ros2_jazzy/build/examples_rclpy_minimal_action_client:/Users/<USER>/ros2_jazzy/install/examples_rclpy_minimal_action_client/lib/python3.11/site-packages:/Users/<USER>/ros2_jazzy/build/examples_rclpy_guard_conditions:/Users/<USER>/ros2_jazzy/install/examples_rclpy_guard_conditions/lib/python3.11/site-packages:/Users/<USER>/ros2_jazzy/build/action_tutorials_py:/Users/<USER>/ros2_jazzy/install/action_tutorials_py/lib/python3.11/site-packages:/Users/<USER>/ros2_jazzy/install/rclpy/lib/python3.11/site-packages:/Users/<USER>/ros2_jazzy/install/rosgraph_msgs/lib/python3.11/site-packages:/Users/<USER>/ros2_jazzy/install/rosbag2_interfaces/lib/python3.11/site-packages:/Users/<USER>/ros2_jazzy/install/ros2cli_test_interfaces/lib/python3.11/site-packages:/Users/<USER>/ros2_jazzy/install/rmw_dds_common/lib/python3.11/site-packages:/Users/<USER>/ros2_jazzy/install/composition_interfaces/lib/python3.11/site-packages:/Users/<USER>/ros2_jazzy/install/rcl_interfaces/lib/python3.11/site-packages:/Users/<USER>/ros2_jazzy/install/pendulum_msgs/lib/python3.11/site-packages:/Users/<USER>/ros2_jazzy/install/lifecycle_msgs/lib/python3.11/site-packages:/Users/<USER>/ros2_jazzy/install/example_interfaces/lib/python3.11/site-packages:/Users/<USER>/ros2_jazzy/install/action_tutorials_interfaces/lib/python3.11/site-packages:/Users/<USER>/ros2_jazzy/install/action_msgs/lib/python3.11/site-packages:/Users/<USER>/ros2_jazzy/install/unique_identifier_msgs/lib/python3.11/site-packages:/Users/<USER>/ros2_jazzy/install/type_description_interfaces/lib/python3.11/site-packages:/Users/<USER>/ros2_jazzy/install/service_msgs/lib/python3.11/site-packages:/Users/<USER>/ros2_jazzy/install/builtin_interfaces/lib/python3.11/site-packages:/Users/<USER>/ros2_jazzy/install/rosidl_generator_py/lib/python3.11/site-packages:/Users/<USER>/ros2_jazzy/build/ament_uncrustify:/Users/<USER>/ros2_jazzy/install/ament_uncrustify/lib/python3.11/site-packages:/Users/<USER>/ros2_jazzy/build/tracetools_trace:/Users/<USER>/ros2_jazzy/install/tracetools_trace/lib/python3.11/site-packages:/Users/<USER>/ros2_jazzy/build/tracetools_read:/Users/<USER>/ros2_jazzy/install/tracetools_read/lib/python3.11/site-packages:/Users/<USER>/ros2_jazzy/build/test_tracetools_launch:/Users/<USER>/ros2_jazzy/install/test_tracetools_launch/lib/python3.11/site-packages:/Users/<USER>/ros2_jazzy/build/test_ros2trace:/Users/<USER>/ros2_jazzy/install/test_ros2trace/lib/python3.11/site-packages:/Users/<USER>/ros2_jazzy/build/test_launch_ros:/Users/<USER>/ros2_jazzy/install/test_launch_ros/lib/python3.11/site-packages:/Users/<USER>/ros2_jazzy/install/lttngpy/lib/python3.11/site-packages:/Users/<USER>/ros2_jazzy/build/rpyutils:/Users/<USER>/ros2_jazzy/install/rpyutils/lib/python3.11/site-packages:/Users/<USER>/ros2_jazzy/install/rosidl_typesupport_cpp/lib/python3.11/site-packages:/Users/<USER>/ros2_jazzy/install/rosidl_typesupport_introspection_cpp/lib/python3.11/site-packages:/Users/<USER>/ros2_jazzy/install/rosidl_typesupport_c/lib/python3.11/site-packages:/Users/<USER>/ros2_jazzy/install/rosidl_typesupport_introspection_c/lib/python3.11/site-packages:/Users/<USER>/ros2_jazzy/install/rosidl_typesupport_fastrtps_c/lib/python3.11/site-packages:/Users/<USER>/ros2_jazzy/install/rosidl_typesupport_fastrtps_cpp/lib/python3.11/site-packages:/Users/<USER>/ros2_jazzy/install/rosidl_generator_cpp/lib/python3.11/site-packages:/Users/<USER>/ros2_jazzy/install/rosidl_generator_c/lib/python3.11/site-packages:/Users/<USER>/ros2_jazzy/build/rosidl_runtime_py:/Users/<USER>/ros2_jazzy/install/rosidl_runtime_py/lib/python3.11/site-packages:/Users/<USER>/ros2_jazzy/install/rosidl_generator_dds_idl/lib/python3.11/site-packages:/Users/<USER>/ros2_jazzy/install/rosidl_cmake/lib/python3.11/site-packages:/Users/<USER>/ros2_jazzy/build/rosidl_pycommon:/Users/<USER>/ros2_jazzy/install/rosidl_pycommon/lib/python3.11/site-packages:/Users/<USER>/ros2_jazzy/install/rosidl_generator_type_description/lib/python3.11/site-packages:/Users/<USER>/ros2_jazzy/install/rosidl_parser/lib/python3.11/site-packages:/Users/<USER>/ros2_jazzy/install/rosidl_adapter/lib/python3.11/site-packages:/Users/<USER>/ros2_jazzy/build/rosidl_cli:/Users/<USER>/ros2_jazzy/install/rosidl_cli/lib/python3.11/site-packages:/Users/<USER>/ros2_jazzy/install/rosbag2_test_msgdefs/lib/python3.11/site-packages:/Users/<USER>/ros2_jazzy/install/resource_retriever/lib/python3.11/site-packages:/Users/<USER>/ros2_jazzy/install/rcutils/lib/python3.11/site-packages:/Users/<USER>/ros2_jazzy/install/python_qt_binding/lib/python3.11/site-packages:/Users/<USER>/ros2_jazzy/install/python_orocos_kdl_vendor/lib/python3.11/dist-packages:/Users/<USER>/ros2_jazzy/build/launch_pytest:/Users/<USER>/ros2_jazzy/install/launch_pytest/lib/python3.11/site-packages:/Users/<USER>/ros2_jazzy/build/launch_testing:/Users/<USER>/ros2_jazzy/install/launch_testing/lib/python3.11/site-packages:/Users/<USER>/ros2_jazzy/build/launch_yaml:/Users/<USER>/ros2_jazzy/install/launch_yaml/lib/python3.11/site-packages:/Users/<USER>/ros2_jazzy/build/launch_xml:/Users/<USER>/ros2_jazzy/install/launch_xml/lib/python3.11/site-packages:/Users/<USER>/ros2_jazzy/build/launch:/Users/<USER>/ros2_jazzy/install/launch/lib/python3.11/site-packages:/Users/<USER>/ros2_jazzy/build/osrf_pycommon:/Users/<USER>/ros2_jazzy/install/osrf_pycommon/lib/python3.11/site-packages:/Users/<USER>/ros2_jazzy/install/ament_cmake_google_benchmark/lib/python3.11/site-packages:/Users/<USER>/ros2_jazzy/build/domain_coordinator:/Users/<USER>/ros2_jazzy/install/domain_coordinator/lib/python3.11/site-packages:/Users/<USER>/ros2_jazzy/build/ament_xmllint:/Users/<USER>/ros2_jazzy/install/ament_xmllint/lib/python3.11/site-packages:/Users/<USER>/ros2_jazzy/build/ament_pyflakes:/Users/<USER>/ros2_jazzy/install/ament_pyflakes/lib/python3.11/site-packages:/Users/<USER>/ros2_jazzy/build/ament_pycodestyle:/Users/<USER>/ros2_jazzy/install/ament_pycodestyle/lib/python3.11/site-packages:/Users/<USER>/ros2_jazzy/build/ament_pep257:/Users/<USER>/ros2_jazzy/install/ament_pep257/lib/python3.11/site-packages:/Users/<USER>/ros2_jazzy/build/ament_pclint:/Users/<USER>/ros2_jazzy/install/ament_pclint/lib/python3.11/site-packages:/Users/<USER>/ros2_jazzy/install/ament_cmake_test/lib/python3.11/site-packages:/Users/<USER>/ros2_jazzy/build/ament_package:/Users/<USER>/ros2_jazzy/install/ament_package/lib/python3.11/site-packages:/Users/<USER>/ros2_jazzy/build/ament_mypy:/Users/<USER>/ros2_jazzy/install/ament_mypy/lib/python3.11/site-packages:/Users/<USER>/ros2_jazzy/build/ament_lint_cmake:/Users/<USER>/ros2_jazzy/install/ament_lint_cmake/lib/python3.11/site-packages:/Users/<USER>/ros2_jazzy/build/ament_flake8:/Users/<USER>/ros2_jazzy/install/ament_flake8/lib/python3.11/site-packages:/Users/<USER>/ros2_jazzy/build/ament_copyright:/Users/<USER>/ros2_jazzy/install/ament_copyright/lib/python3.11/site-packages:/Users/<USER>/ros2_jazzy/build/ament_lint:/Users/<USER>/ros2_jazzy/install/ament_lint/lib/python3.11/site-packages:/Users/<USER>/ros2_jazzy/build/ament_index_python:/Users/<USER>/ros2_jazzy/install/ament_index_python/lib/python3.11/site-packages:/Users/<USER>/ros2_jazzy/build/ament_cpplint:/Users/<USER>/ros2_jazzy/install/ament_cpplint/lib/python3.11/site-packages:/Users/<USER>/ros2_jazzy/build/ament_cppcheck:/Users/<USER>/ros2_jazzy/install/ament_cppcheck/lib/python3.11/site-packages:/Users/<USER>/ros2_jazzy/build/ament_clang_tidy:/Users/<USER>/ros2_jazzy/install/ament_clang_tidy/lib/python3.11/site-packages:/Users/<USER>/ros2_jazzy/build/ament_clang_format:/Users/<USER>/ros2_jazzy/install/ament_clang_format/lib/python3.11/site-packages:/Users/<USER>/Work/drill2/onboard/build/drill_regulator:/Users/<USER>/Work/drill2/onboard/install/drill_regulator/lib/python3.11/site-packages SHLVL=2 VIRTUAL_ENV_PROMPT=(.ros2_venv) _=/usr/bin/env /Users/<USER>/.ros2_venv/bin/python3 -W ignore:setup.py install is deprecated -W ignore:easy_install command is deprecated setup.py develop --editable --build-directory /Users/<USER>/Work/drill2/onboard/build/tracks_regulator/build --no-deps symlink_data
