running develop
running egg_info
writing driller.egg-info/PKG-INFO
writing dependency_links to driller.egg-info/dependency_links.txt
writing entry points to driller.egg-info/entry_points.txt
writing requirements to driller.egg-info/requires.txt
writing top-level names to driller.egg-info/top_level.txt
reading manifest file 'driller.egg-info/SOURCES.txt'
writing manifest file 'driller.egg-info/SOURCES.txt'
running build_ext
Creating /Users/<USER>/Work/drill2/onboard/install/driller/lib/python3.11/site-packages/driller.egg-link (link to .)
Installing driller script to /Users/<USER>/Work/drill2/onboard/install/driller/bin

Installed /Users/<USER>/Work/drill2/onboard/build/driller
running symlink_data
