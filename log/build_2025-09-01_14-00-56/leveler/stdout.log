running develop
running egg_info
writing leveler.egg-info/PKG-INFO
writing dependency_links to leveler.egg-info/dependency_links.txt
writing entry points to leveler.egg-info/entry_points.txt
writing requirements to leveler.egg-info/requires.txt
writing top-level names to leveler.egg-info/top_level.txt
reading manifest file 'leveler.egg-info/SOURCES.txt'
writing manifest file 'leveler.egg-info/SOURCES.txt'
running build_ext
Creating /Users/<USER>/Work/drill2/onboard/install/leveler/lib/python3.11/site-packages/leveler.egg-link (link to .)
Installing leveler_node script to /Users/<USER>/Work/drill2/onboard/install/leveler/lib/leveler

Installed /Users/<USER>/Work/drill2/onboard/build/leveler
running symlink_data
