[0.300s] Invoking command in '/Users/<USER>/Work/drill2/onboard/build/can_msgs': CMAKE_PREFIX_PATH=${CMAKE_PREFIX_PATH}:/Users/<USER>/Work/drill2/onboard/install/remote_connector:/Users/<USER>/Work/drill2/onboard/install/tracks_regulator:/Users/<USER>/Work/drill2/onboard/install/state_tracker:/Users/<USER>/Work/drill2/onboard/install/rtk_connector:/Users/<USER>/Work/drill2/onboard/install/path_follower:/Users/<USER>/Work/drill2/onboard/install/params_server:/Users/<USER>/Work/drill2/onboard/install/leveler:/Users/<USER>/Work/drill2/onboard/install/launchpack:/Users/<USER>/Work/drill2/onboard/install/driller:/Users/<USER>/Work/drill2/onboard/install/depth_tracker:/Users/<USER>/Work/drill2/onboard/install/can_encoder:/Users/<USER>/Work/drill2/onboard/install/can_decoder:/Users/<USER>/Work/drill2/onboard/install/arm_controller:/Users/<USER>/Work/drill2/onboard/install/base_node:/Users/<USER>/ros2_jazzy/install/rosbag2_examples_py:/Users/<USER>/ros2_jazzy/install/launch_testing_examples:/Users/<USER>/ros2_jazzy/install/ros2bag:/Users/<USER>/ros2_jazzy/install/tracetools_test:/Users/<USER>/ros2_jazzy/install/tracetools_launch:/Users/<USER>/ros2_jazzy/install/topic_monitor:/Users/<USER>/ros2_jazzy/install/tf2_tools:/Users/<USER>/ros2_jazzy/install/examples_tf2_py:/Users/<USER>/ros2_jazzy/install/tf2_ros_py:/Users/<USER>/ros2_jazzy/install/examples_rclpy_pointcloud_publisher:/Users/<USER>/ros2_jazzy/install/sensor_msgs_py:/Users/<USER>/ros2_jazzy/install/quality_of_service_demo_py:/Users/<USER>/ros2_jazzy/install/camera_info_manager_py:/Users/<USER>/ros2_jazzy/install/ros2doctor:/Users/<USER>/ros2_jazzy/install/lifecycle_py:/Users/<USER>/ros2_jazzy/install/examples_rclpy_minimal_subscriber:/Users/<USER>/ros2_jazzy/install/examples_rclpy_minimal_service:/Users/<USER>/ros2_jazzy/install/examples_rclpy_minimal_publisher:/Users/<USER>/ros2_jazzy/install/examples_rclpy_minimal_client:/Users/<USER>/ros2_jazzy/install/examples_rclpy_executors:/Users/<USER>/ros2_jazzy/install/demo_nodes_py:/Users/<USER>/ros2_jazzy/install/ros2component:/Users/<USER>/ros2_jazzy/install/sros2:/Users/<USER>/ros2_jazzy/install/ros2trace:/Users/<USER>/ros2_jazzy/install/ros2topic:/Users/<USER>/ros2_jazzy/install/ros2test:/Users/<USER>/ros2_jazzy/install/ros2param:/Users/<USER>/ros2_jazzy/install/ros2lifecycle:/Users/<USER>/ros2_jazzy/install/ros2service:/Users/<USER>/ros2_jazzy/install/ros2run:/Users/<USER>/ros2_jazzy/install/ros2launch:/Users/<USER>/ros2_jazzy/install/ros2pkg:/Users/<USER>/ros2_jazzy/install/ros2node:/Users/<USER>/ros2_jazzy/install/ros2multicast:/Users/<USER>/ros2_jazzy/install/ros2interface:/Users/<USER>/ros2_jazzy/install/ros2action:/Users/<USER>/ros2_jazzy/install/ros2cli:/Users/<USER>/ros2_jazzy/install/launch_testing_ros:/Users/<USER>/ros2_jazzy/install/launch_ros:/Users/<USER>/ros2_jazzy/install/examples_rclpy_minimal_action_server:/Users/<USER>/ros2_jazzy/install/examples_rclpy_minimal_action_client:/Users/<USER>/ros2_jazzy/install/examples_rclpy_guard_conditions:/Users/<USER>/ros2_jazzy/install/action_tutorials_py:/Users/<USER>/ros2_jazzy/install/ament_uncrustify:/Users/<USER>/ros2_jazzy/install/tracetools_trace:/Users/<USER>/ros2_jazzy/install/tracetools_read:/Users/<USER>/ros2_jazzy/install/test_tracetools_launch:/Users/<USER>/ros2_jazzy/install/test_ros2trace:/Users/<USER>/ros2_jazzy/install/test_launch_ros:/Users/<USER>/ros2_jazzy/install/rpyutils:/Users/<USER>/ros2_jazzy/install/rosidl_runtime_py:/Users/<USER>/ros2_jazzy/install/rosidl_pycommon:/Users/<USER>/ros2_jazzy/install/rosidl_cli:/Users/<USER>/ros2_jazzy/install/launch_pytest:/Users/<USER>/ros2_jazzy/install/launch_testing:/Users/<USER>/ros2_jazzy/install/launch_yaml:/Users/<USER>/ros2_jazzy/install/launch_xml:/Users/<USER>/ros2_jazzy/install/launch:/Users/<USER>/ros2_jazzy/install/osrf_pycommon:/Users/<USER>/ros2_jazzy/install/domain_coordinator:/Users/<USER>/ros2_jazzy/install/ament_xmllint:/Users/<USER>/ros2_jazzy/install/ament_pyflakes:/Users/<USER>/ros2_jazzy/install/ament_pycodestyle:/Users/<USER>/ros2_jazzy/install/ament_pep257:/Users/<USER>/ros2_jazzy/install/ament_pclint:/Users/<USER>/ros2_jazzy/install/ament_package:/Users/<USER>/ros2_jazzy/install/ament_mypy:/Users/<USER>/ros2_jazzy/install/ament_lint_cmake:/Users/<USER>/ros2_jazzy/install/ament_flake8:/Users/<USER>/ros2_jazzy/install/ament_copyright:/Users/<USER>/ros2_jazzy/install/ament_lint:/Users/<USER>/ros2_jazzy/install/ament_index_python:/Users/<USER>/ros2_jazzy/install/ament_cpplint:/Users/<USER>/ros2_jazzy/install/ament_cppcheck:/Users/<USER>/ros2_jazzy/install/ament_clang_tidy:/Users/<USER>/ros2_jazzy/install/ament_clang_format SHLVL=2 VIRTUAL_ENV_PROMPT=(.ros2_venv) _=/usr/bin/env /opt/homebrew/bin/cmake --build /Users/<USER>/Work/drill2/onboard/build/can_msgs -- -j8 -l8
[1.809s] [  2%] Built target can_msgs__rosidl_generator_type_description
[2.095s] [  2%] Built target ament_cmake_python_symlink_can_msgs
[3.455s] [ 17%] Built target can_msgs__rosidl_generator_c
[5.078s] running egg_info
[5.081s] writing can_msgs.egg-info/PKG-INFO
[5.085s] writing dependency_links to can_msgs.egg-info/dependency_links.txt
[5.085s] writing top-level names to can_msgs.egg-info/top_level.txt
[5.088s] reading manifest file 'can_msgs.egg-info/SOURCES.txt'
[5.088s] writing manifest file 'can_msgs.egg-info/SOURCES.txt'
[5.378s] [ 17%] Built target ament_cmake_python_build_can_msgs_egg
[5.813s] [ 26%] Built target can_msgs__rosidl_typesupport_c
[6.172s] [ 35%] Built target can_msgs__rosidl_typesupport_fastrtps_c
[6.537s] [ 44%] Built target can_msgs__rosidl_typesupport_introspection_c
[6.953s] [ 47%] Built target can_msgs__cpp
[7.643s] [ 55%] Built target can_msgs__rosidl_typesupport_cpp
[8.099s] [ 64%] Built target can_msgs__rosidl_typesupport_fastrtps_cpp
[8.558s] [ 73%] Built target can_msgs__rosidl_typesupport_introspection_cpp
[8.937s] [ 73%] Built target can_msgs
[9.242s] [ 76%] Built target can_msgs__py
[9.625s] [ 82%] Built target can_msgs__rosidl_generator_py
[10.041s] [ 88%] Built target can_msgs_s__rosidl_typesupport_introspection_c
[10.414s] [ 94%] Built target can_msgs_s__rosidl_typesupport_c
[10.775s] [100%] Built target can_msgs_s__rosidl_typesupport_fastrtps_c
[10.827s] Invoked command in '/Users/<USER>/Work/drill2/onboard/build/can_msgs' returned '0': CMAKE_PREFIX_PATH=${CMAKE_PREFIX_PATH}:/Users/<USER>/Work/drill2/onboard/install/remote_connector:/Users/<USER>/Work/drill2/onboard/install/tracks_regulator:/Users/<USER>/Work/drill2/onboard/install/state_tracker:/Users/<USER>/Work/drill2/onboard/install/rtk_connector:/Users/<USER>/Work/drill2/onboard/install/path_follower:/Users/<USER>/Work/drill2/onboard/install/params_server:/Users/<USER>/Work/drill2/onboard/install/leveler:/Users/<USER>/Work/drill2/onboard/install/launchpack:/Users/<USER>/Work/drill2/onboard/install/driller:/Users/<USER>/Work/drill2/onboard/install/depth_tracker:/Users/<USER>/Work/drill2/onboard/install/can_encoder:/Users/<USER>/Work/drill2/onboard/install/can_decoder:/Users/<USER>/Work/drill2/onboard/install/arm_controller:/Users/<USER>/Work/drill2/onboard/install/base_node:/Users/<USER>/ros2_jazzy/install/rosbag2_examples_py:/Users/<USER>/ros2_jazzy/install/launch_testing_examples:/Users/<USER>/ros2_jazzy/install/ros2bag:/Users/<USER>/ros2_jazzy/install/tracetools_test:/Users/<USER>/ros2_jazzy/install/tracetools_launch:/Users/<USER>/ros2_jazzy/install/topic_monitor:/Users/<USER>/ros2_jazzy/install/tf2_tools:/Users/<USER>/ros2_jazzy/install/examples_tf2_py:/Users/<USER>/ros2_jazzy/install/tf2_ros_py:/Users/<USER>/ros2_jazzy/install/examples_rclpy_pointcloud_publisher:/Users/<USER>/ros2_jazzy/install/sensor_msgs_py:/Users/<USER>/ros2_jazzy/install/quality_of_service_demo_py:/Users/<USER>/ros2_jazzy/install/camera_info_manager_py:/Users/<USER>/ros2_jazzy/install/ros2doctor:/Users/<USER>/ros2_jazzy/install/lifecycle_py:/Users/<USER>/ros2_jazzy/install/examples_rclpy_minimal_subscriber:/Users/<USER>/ros2_jazzy/install/examples_rclpy_minimal_service:/Users/<USER>/ros2_jazzy/install/examples_rclpy_minimal_publisher:/Users/<USER>/ros2_jazzy/install/examples_rclpy_minimal_client:/Users/<USER>/ros2_jazzy/install/examples_rclpy_executors:/Users/<USER>/ros2_jazzy/install/demo_nodes_py:/Users/<USER>/ros2_jazzy/install/ros2component:/Users/<USER>/ros2_jazzy/install/sros2:/Users/<USER>/ros2_jazzy/install/ros2trace:/Users/<USER>/ros2_jazzy/install/ros2topic:/Users/<USER>/ros2_jazzy/install/ros2test:/Users/<USER>/ros2_jazzy/install/ros2param:/Users/<USER>/ros2_jazzy/install/ros2lifecycle:/Users/<USER>/ros2_jazzy/install/ros2service:/Users/<USER>/ros2_jazzy/install/ros2run:/Users/<USER>/ros2_jazzy/install/ros2launch:/Users/<USER>/ros2_jazzy/install/ros2pkg:/Users/<USER>/ros2_jazzy/install/ros2node:/Users/<USER>/ros2_jazzy/install/ros2multicast:/Users/<USER>/ros2_jazzy/install/ros2interface:/Users/<USER>/ros2_jazzy/install/ros2action:/Users/<USER>/ros2_jazzy/install/ros2cli:/Users/<USER>/ros2_jazzy/install/launch_testing_ros:/Users/<USER>/ros2_jazzy/install/launch_ros:/Users/<USER>/ros2_jazzy/install/examples_rclpy_minimal_action_server:/Users/<USER>/ros2_jazzy/install/examples_rclpy_minimal_action_client:/Users/<USER>/ros2_jazzy/install/examples_rclpy_guard_conditions:/Users/<USER>/ros2_jazzy/install/action_tutorials_py:/Users/<USER>/ros2_jazzy/install/ament_uncrustify:/Users/<USER>/ros2_jazzy/install/tracetools_trace:/Users/<USER>/ros2_jazzy/install/tracetools_read:/Users/<USER>/ros2_jazzy/install/test_tracetools_launch:/Users/<USER>/ros2_jazzy/install/test_ros2trace:/Users/<USER>/ros2_jazzy/install/test_launch_ros:/Users/<USER>/ros2_jazzy/install/rpyutils:/Users/<USER>/ros2_jazzy/install/rosidl_runtime_py:/Users/<USER>/ros2_jazzy/install/rosidl_pycommon:/Users/<USER>/ros2_jazzy/install/rosidl_cli:/Users/<USER>/ros2_jazzy/install/launch_pytest:/Users/<USER>/ros2_jazzy/install/launch_testing:/Users/<USER>/ros2_jazzy/install/launch_yaml:/Users/<USER>/ros2_jazzy/install/launch_xml:/Users/<USER>/ros2_jazzy/install/launch:/Users/<USER>/ros2_jazzy/install/osrf_pycommon:/Users/<USER>/ros2_jazzy/install/domain_coordinator:/Users/<USER>/ros2_jazzy/install/ament_xmllint:/Users/<USER>/ros2_jazzy/install/ament_pyflakes:/Users/<USER>/ros2_jazzy/install/ament_pycodestyle:/Users/<USER>/ros2_jazzy/install/ament_pep257:/Users/<USER>/ros2_jazzy/install/ament_pclint:/Users/<USER>/ros2_jazzy/install/ament_package:/Users/<USER>/ros2_jazzy/install/ament_mypy:/Users/<USER>/ros2_jazzy/install/ament_lint_cmake:/Users/<USER>/ros2_jazzy/install/ament_flake8:/Users/<USER>/ros2_jazzy/install/ament_copyright:/Users/<USER>/ros2_jazzy/install/ament_lint:/Users/<USER>/ros2_jazzy/install/ament_index_python:/Users/<USER>/ros2_jazzy/install/ament_cpplint:/Users/<USER>/ros2_jazzy/install/ament_cppcheck:/Users/<USER>/ros2_jazzy/install/ament_clang_tidy:/Users/<USER>/ros2_jazzy/install/ament_clang_format SHLVL=2 VIRTUAL_ENV_PROMPT=(.ros2_venv) _=/usr/bin/env /opt/homebrew/bin/cmake --build /Users/<USER>/Work/drill2/onboard/build/can_msgs -- -j8 -l8
[10.889s] Invoking command in '/Users/<USER>/Work/drill2/onboard/build/can_msgs': CMAKE_PREFIX_PATH=${CMAKE_PREFIX_PATH}:/Users/<USER>/Work/drill2/onboard/install/remote_connector:/Users/<USER>/Work/drill2/onboard/install/tracks_regulator:/Users/<USER>/Work/drill2/onboard/install/state_tracker:/Users/<USER>/Work/drill2/onboard/install/rtk_connector:/Users/<USER>/Work/drill2/onboard/install/path_follower:/Users/<USER>/Work/drill2/onboard/install/params_server:/Users/<USER>/Work/drill2/onboard/install/leveler:/Users/<USER>/Work/drill2/onboard/install/launchpack:/Users/<USER>/Work/drill2/onboard/install/driller:/Users/<USER>/Work/drill2/onboard/install/depth_tracker:/Users/<USER>/Work/drill2/onboard/install/can_encoder:/Users/<USER>/Work/drill2/onboard/install/can_decoder:/Users/<USER>/Work/drill2/onboard/install/arm_controller:/Users/<USER>/Work/drill2/onboard/install/base_node:/Users/<USER>/ros2_jazzy/install/rosbag2_examples_py:/Users/<USER>/ros2_jazzy/install/launch_testing_examples:/Users/<USER>/ros2_jazzy/install/ros2bag:/Users/<USER>/ros2_jazzy/install/tracetools_test:/Users/<USER>/ros2_jazzy/install/tracetools_launch:/Users/<USER>/ros2_jazzy/install/topic_monitor:/Users/<USER>/ros2_jazzy/install/tf2_tools:/Users/<USER>/ros2_jazzy/install/examples_tf2_py:/Users/<USER>/ros2_jazzy/install/tf2_ros_py:/Users/<USER>/ros2_jazzy/install/examples_rclpy_pointcloud_publisher:/Users/<USER>/ros2_jazzy/install/sensor_msgs_py:/Users/<USER>/ros2_jazzy/install/quality_of_service_demo_py:/Users/<USER>/ros2_jazzy/install/camera_info_manager_py:/Users/<USER>/ros2_jazzy/install/ros2doctor:/Users/<USER>/ros2_jazzy/install/lifecycle_py:/Users/<USER>/ros2_jazzy/install/examples_rclpy_minimal_subscriber:/Users/<USER>/ros2_jazzy/install/examples_rclpy_minimal_service:/Users/<USER>/ros2_jazzy/install/examples_rclpy_minimal_publisher:/Users/<USER>/ros2_jazzy/install/examples_rclpy_minimal_client:/Users/<USER>/ros2_jazzy/install/examples_rclpy_executors:/Users/<USER>/ros2_jazzy/install/demo_nodes_py:/Users/<USER>/ros2_jazzy/install/ros2component:/Users/<USER>/ros2_jazzy/install/sros2:/Users/<USER>/ros2_jazzy/install/ros2trace:/Users/<USER>/ros2_jazzy/install/ros2topic:/Users/<USER>/ros2_jazzy/install/ros2test:/Users/<USER>/ros2_jazzy/install/ros2param:/Users/<USER>/ros2_jazzy/install/ros2lifecycle:/Users/<USER>/ros2_jazzy/install/ros2service:/Users/<USER>/ros2_jazzy/install/ros2run:/Users/<USER>/ros2_jazzy/install/ros2launch:/Users/<USER>/ros2_jazzy/install/ros2pkg:/Users/<USER>/ros2_jazzy/install/ros2node:/Users/<USER>/ros2_jazzy/install/ros2multicast:/Users/<USER>/ros2_jazzy/install/ros2interface:/Users/<USER>/ros2_jazzy/install/ros2action:/Users/<USER>/ros2_jazzy/install/ros2cli:/Users/<USER>/ros2_jazzy/install/launch_testing_ros:/Users/<USER>/ros2_jazzy/install/launch_ros:/Users/<USER>/ros2_jazzy/install/examples_rclpy_minimal_action_server:/Users/<USER>/ros2_jazzy/install/examples_rclpy_minimal_action_client:/Users/<USER>/ros2_jazzy/install/examples_rclpy_guard_conditions:/Users/<USER>/ros2_jazzy/install/action_tutorials_py:/Users/<USER>/ros2_jazzy/install/ament_uncrustify:/Users/<USER>/ros2_jazzy/install/tracetools_trace:/Users/<USER>/ros2_jazzy/install/tracetools_read:/Users/<USER>/ros2_jazzy/install/test_tracetools_launch:/Users/<USER>/ros2_jazzy/install/test_ros2trace:/Users/<USER>/ros2_jazzy/install/test_launch_ros:/Users/<USER>/ros2_jazzy/install/rpyutils:/Users/<USER>/ros2_jazzy/install/rosidl_runtime_py:/Users/<USER>/ros2_jazzy/install/rosidl_pycommon:/Users/<USER>/ros2_jazzy/install/rosidl_cli:/Users/<USER>/ros2_jazzy/install/launch_pytest:/Users/<USER>/ros2_jazzy/install/launch_testing:/Users/<USER>/ros2_jazzy/install/launch_yaml:/Users/<USER>/ros2_jazzy/install/launch_xml:/Users/<USER>/ros2_jazzy/install/launch:/Users/<USER>/ros2_jazzy/install/osrf_pycommon:/Users/<USER>/ros2_jazzy/install/domain_coordinator:/Users/<USER>/ros2_jazzy/install/ament_xmllint:/Users/<USER>/ros2_jazzy/install/ament_pyflakes:/Users/<USER>/ros2_jazzy/install/ament_pycodestyle:/Users/<USER>/ros2_jazzy/install/ament_pep257:/Users/<USER>/ros2_jazzy/install/ament_pclint:/Users/<USER>/ros2_jazzy/install/ament_package:/Users/<USER>/ros2_jazzy/install/ament_mypy:/Users/<USER>/ros2_jazzy/install/ament_lint_cmake:/Users/<USER>/ros2_jazzy/install/ament_flake8:/Users/<USER>/ros2_jazzy/install/ament_copyright:/Users/<USER>/ros2_jazzy/install/ament_lint:/Users/<USER>/ros2_jazzy/install/ament_index_python:/Users/<USER>/ros2_jazzy/install/ament_cpplint:/Users/<USER>/ros2_jazzy/install/ament_cppcheck:/Users/<USER>/ros2_jazzy/install/ament_clang_tidy:/Users/<USER>/ros2_jazzy/install/ament_clang_format SHLVL=2 VIRTUAL_ENV_PROMPT=(.ros2_venv) _=/usr/bin/env /opt/homebrew/bin/cmake --install /Users/<USER>/Work/drill2/onboard/build/can_msgs
[10.936s] -- Install configuration: ""
[10.937s] -- Execute custom install script
[10.937s] -- Up-to-date symlink: /Users/<USER>/Work/drill2/onboard/install/can_msgs/share/ament_index/resource_index/rosidl_interfaces/can_msgs
[10.937s] -- Up-to-date symlink: /Users/<USER>/Work/drill2/onboard/install/can_msgs/share/can_msgs/msg/Frame.json
[10.938s] -- Up-to-date symlink: /Users/<USER>/Work/drill2/onboard/install/can_msgs/include/can_msgs/can_msgs/msg/detail/frame__functions.h
[10.938s] -- Up-to-date symlink: /Users/<USER>/Work/drill2/onboard/install/can_msgs/include/can_msgs/can_msgs/msg/detail/frame__struct.h
[10.938s] -- Up-to-date symlink: /Users/<USER>/Work/drill2/onboard/install/can_msgs/include/can_msgs/can_msgs/msg/detail/frame__type_support.h
[10.938s] -- Up-to-date symlink: /Users/<USER>/Work/drill2/onboard/install/can_msgs/include/can_msgs/can_msgs/msg/frame.h
[10.938s] -- Up-to-date symlink: /Users/<USER>/Work/drill2/onboard/install/can_msgs/include/can_msgs/can_msgs/msg/rosidl_generator_c__visibility_control.h
[10.938s] -- Up-to-date symlink: /Users/<USER>/Work/drill2/onboard/install/can_msgs/share/can_msgs/environment/library_path.sh
[10.939s] -- Up-to-date symlink: /Users/<USER>/Work/drill2/onboard/install/can_msgs/share/can_msgs/environment/library_path.dsv
[10.939s] -- Up-to-date symlink: /Users/<USER>/Work/drill2/onboard/install/can_msgs/include/can_msgs/can_msgs/msg/detail/frame__rosidl_typesupport_introspection_c.h
[10.939s] -- Up-to-date symlink: /Users/<USER>/Work/drill2/onboard/install/can_msgs/include/can_msgs/can_msgs/msg/rosidl_typesupport_introspection_c__visibility_control.h
[10.940s] -- Up-to-date symlink: /Users/<USER>/Work/drill2/onboard/install/can_msgs/include/can_msgs/can_msgs/msg/detail/frame__rosidl_typesupport_fastrtps_c.h
[10.940s] -- Up-to-date symlink: /Users/<USER>/Work/drill2/onboard/install/can_msgs/include/can_msgs/can_msgs/msg/rosidl_typesupport_fastrtps_c__visibility_control.h
[10.940s] -- Up-to-date symlink: /Users/<USER>/Work/drill2/onboard/install/can_msgs/include/can_msgs/can_msgs/msg/detail/frame__builder.hpp
[10.940s] -- Up-to-date symlink: /Users/<USER>/Work/drill2/onboard/install/can_msgs/include/can_msgs/can_msgs/msg/detail/frame__struct.hpp
[10.940s] -- Up-to-date symlink: /Users/<USER>/Work/drill2/onboard/install/can_msgs/include/can_msgs/can_msgs/msg/detail/frame__traits.hpp
[10.940s] -- Up-to-date symlink: /Users/<USER>/Work/drill2/onboard/install/can_msgs/include/can_msgs/can_msgs/msg/detail/frame__type_support.hpp
[10.940s] -- Up-to-date symlink: /Users/<USER>/Work/drill2/onboard/install/can_msgs/include/can_msgs/can_msgs/msg/frame.hpp
[10.941s] -- Up-to-date symlink: /Users/<USER>/Work/drill2/onboard/install/can_msgs/include/can_msgs/can_msgs/msg/rosidl_generator_cpp__visibility_control.hpp
[10.942s] -- Up-to-date symlink: /Users/<USER>/Work/drill2/onboard/install/can_msgs/include/can_msgs/can_msgs/msg/detail/frame__rosidl_typesupport_introspection_cpp.hpp
[10.942s] -- Up-to-date symlink: /Users/<USER>/Work/drill2/onboard/install/can_msgs/include/can_msgs/can_msgs/msg/detail/frame__rosidl_typesupport_fastrtps_cpp.hpp
[10.942s] -- Up-to-date symlink: /Users/<USER>/Work/drill2/onboard/install/can_msgs/include/can_msgs/can_msgs/msg/rosidl_typesupport_fastrtps_cpp__visibility_control.h
[10.946s] -- Up-to-date symlink: /Users/<USER>/Work/drill2/onboard/install/can_msgs/share/can_msgs/environment/pythonpath.sh
[10.947s] -- Up-to-date symlink: /Users/<USER>/Work/drill2/onboard/install/can_msgs/share/can_msgs/environment/pythonpath.dsv
[10.947s] -- Up-to-date symlink: /Users/<USER>/Work/drill2/onboard/install/can_msgs/lib/python3.11/site-packages/can_msgs-2.0.0-py3.11.egg-info/PKG-INFO
[10.947s] -- Up-to-date symlink: /Users/<USER>/Work/drill2/onboard/install/can_msgs/lib/python3.11/site-packages/can_msgs-2.0.0-py3.11.egg-info/SOURCES.txt
[10.947s] -- Up-to-date symlink: /Users/<USER>/Work/drill2/onboard/install/can_msgs/lib/python3.11/site-packages/can_msgs-2.0.0-py3.11.egg-info/dependency_links.txt
[10.948s] -- Up-to-date symlink: /Users/<USER>/Work/drill2/onboard/install/can_msgs/lib/python3.11/site-packages/can_msgs-2.0.0-py3.11.egg-info/top_level.txt
[10.948s] -- Up-to-date symlink: /Users/<USER>/Work/drill2/onboard/install/can_msgs/lib/python3.11/site-packages/can_msgs/__init__.py
[10.948s] -- Up-to-date symlink: /Users/<USER>/Work/drill2/onboard/install/can_msgs/lib/python3.11/site-packages/can_msgs/_can_msgs_s.ep.rosidl_typesupport_c.c
[10.948s] -- Up-to-date symlink: /Users/<USER>/Work/drill2/onboard/install/can_msgs/lib/python3.11/site-packages/can_msgs/_can_msgs_s.ep.rosidl_typesupport_fastrtps_c.c
[10.948s] -- Up-to-date symlink: /Users/<USER>/Work/drill2/onboard/install/can_msgs/lib/python3.11/site-packages/can_msgs/_can_msgs_s.ep.rosidl_typesupport_introspection_c.c
[10.948s] -- Up-to-date symlink: /Users/<USER>/Work/drill2/onboard/install/can_msgs/lib/python3.11/site-packages/can_msgs/can_msgs_s__rosidl_typesupport_c.so
[10.948s] -- Up-to-date symlink: /Users/<USER>/Work/drill2/onboard/install/can_msgs/lib/python3.11/site-packages/can_msgs/can_msgs_s__rosidl_typesupport_fastrtps_c.so
[10.948s] -- Up-to-date symlink: /Users/<USER>/Work/drill2/onboard/install/can_msgs/lib/python3.11/site-packages/can_msgs/can_msgs_s__rosidl_typesupport_introspection_c.so
[10.948s] -- Up-to-date symlink: /Users/<USER>/Work/drill2/onboard/install/can_msgs/lib/python3.11/site-packages/can_msgs/msg/__init__.py
[10.948s] -- Up-to-date symlink: /Users/<USER>/Work/drill2/onboard/install/can_msgs/lib/python3.11/site-packages/can_msgs/msg/_frame.py
[10.948s] -- Up-to-date symlink: /Users/<USER>/Work/drill2/onboard/install/can_msgs/lib/python3.11/site-packages/can_msgs/msg/_frame_s.c
[10.948s] -- Up-to-date symlink: /Users/<USER>/Work/drill2/onboard/install/can_msgs/lib/python3.11/site-packages/can_msgs/can_msgs_s__rosidl_typesupport_introspection_c.so
[10.949s] -- Up-to-date symlink: /Users/<USER>/Work/drill2/onboard/install/can_msgs/lib/python3.11/site-packages/can_msgs/can_msgs_s__rosidl_typesupport_fastrtps_c.so
[10.949s] -- Up-to-date symlink: /Users/<USER>/Work/drill2/onboard/install/can_msgs/lib/python3.11/site-packages/can_msgs/can_msgs_s__rosidl_typesupport_c.so
[10.949s] -- Up-to-date symlink: /Users/<USER>/Work/drill2/onboard/install/can_msgs/share/can_msgs/msg/Frame.idl
[10.950s] -- Up-to-date symlink: /Users/<USER>/Work/drill2/onboard/install/can_msgs/share/can_msgs/msg/Frame.msg
[10.950s] -- Up-to-date symlink: /Users/<USER>/Work/drill2/onboard/install/can_msgs/share/ament_index/resource_index/package_run_dependencies/can_msgs
[10.950s] -- Up-to-date symlink: /Users/<USER>/Work/drill2/onboard/install/can_msgs/share/ament_index/resource_index/parent_prefix_path/can_msgs
[10.950s] -- Up-to-date symlink: /Users/<USER>/Work/drill2/onboard/install/can_msgs/share/can_msgs/environment/ament_prefix_path.sh
[10.950s] -- Up-to-date symlink: /Users/<USER>/Work/drill2/onboard/install/can_msgs/share/can_msgs/environment/ament_prefix_path.dsv
[10.950s] -- Up-to-date symlink: /Users/<USER>/Work/drill2/onboard/install/can_msgs/share/can_msgs/environment/path.sh
[10.951s] -- Up-to-date symlink: /Users/<USER>/Work/drill2/onboard/install/can_msgs/share/can_msgs/environment/path.dsv
[10.952s] -- Up-to-date symlink: /Users/<USER>/Work/drill2/onboard/install/can_msgs/share/can_msgs/local_setup.bash
[10.952s] -- Up-to-date symlink: /Users/<USER>/Work/drill2/onboard/install/can_msgs/share/can_msgs/local_setup.sh
[10.952s] -- Up-to-date symlink: /Users/<USER>/Work/drill2/onboard/install/can_msgs/share/can_msgs/local_setup.zsh
[10.953s] -- Up-to-date symlink: /Users/<USER>/Work/drill2/onboard/install/can_msgs/share/can_msgs/local_setup.dsv
[10.953s] -- Symlinking: /Users/<USER>/Work/drill2/onboard/install/can_msgs/share/can_msgs/package.dsv
[11.000s] -- Up-to-date symlink: /Users/<USER>/Work/drill2/onboard/install/can_msgs/share/ament_index/resource_index/packages/can_msgs
[11.000s] -- Up-to-date symlink: /Users/<USER>/Work/drill2/onboard/install/can_msgs/share/can_msgs/cmake/rosidl_cmake-extras.cmake
[11.000s] -- Up-to-date symlink: /Users/<USER>/Work/drill2/onboard/install/can_msgs/share/can_msgs/cmake/ament_cmake_export_dependencies-extras.cmake
[11.000s] -- Up-to-date symlink: /Users/<USER>/Work/drill2/onboard/install/can_msgs/share/can_msgs/cmake/ament_cmake_export_include_directories-extras.cmake
[11.000s] -- Up-to-date symlink: /Users/<USER>/Work/drill2/onboard/install/can_msgs/share/can_msgs/cmake/ament_cmake_export_libraries-extras.cmake
[11.002s] -- Up-to-date symlink: /Users/<USER>/Work/drill2/onboard/install/can_msgs/share/can_msgs/cmake/ament_cmake_export_targets-extras.cmake
[11.003s] -- Up-to-date symlink: /Users/<USER>/Work/drill2/onboard/install/can_msgs/share/can_msgs/cmake/rosidl_cmake_export_typesupport_targets-extras.cmake
[11.003s] -- Up-to-date symlink: /Users/<USER>/Work/drill2/onboard/install/can_msgs/share/can_msgs/cmake/rosidl_cmake_export_typesupport_libraries-extras.cmake
[11.003s] -- Up-to-date symlink: /Users/<USER>/Work/drill2/onboard/install/can_msgs/share/can_msgs/cmake/can_msgsConfig.cmake
[11.003s] -- Up-to-date symlink: /Users/<USER>/Work/drill2/onboard/install/can_msgs/share/can_msgs/cmake/can_msgsConfig-version.cmake
[11.003s] -- Up-to-date symlink: /Users/<USER>/Work/drill2/onboard/install/can_msgs/share/can_msgs/package.xml
[11.003s] -- Installing: /Users/<USER>/Work/drill2/onboard/install/can_msgs/lib/libcan_msgs__rosidl_generator_c.dylib
[11.102s] -- Installing: /Users/<USER>/Work/drill2/onboard/install/can_msgs/lib/libcan_msgs__rosidl_typesupport_introspection_c.dylib
[11.206s] -- Installing: /Users/<USER>/Work/drill2/onboard/install/can_msgs/lib/libcan_msgs__rosidl_typesupport_fastrtps_c.dylib
[11.300s] -- Installing: /Users/<USER>/Work/drill2/onboard/install/can_msgs/lib/libcan_msgs__rosidl_typesupport_c.dylib
[11.416s] -- Installing: /Users/<USER>/Work/drill2/onboard/install/can_msgs/lib/libcan_msgs__rosidl_typesupport_introspection_cpp.dylib
[11.512s] -- Installing: /Users/<USER>/Work/drill2/onboard/install/can_msgs/lib/libcan_msgs__rosidl_typesupport_fastrtps_cpp.dylib
[11.605s] -- Installing: /Users/<USER>/Work/drill2/onboard/install/can_msgs/lib/libcan_msgs__rosidl_typesupport_cpp.dylib
[12.086s] Listing '/Users/<USER>/Work/drill2/onboard/install/can_msgs/lib/python3.11/site-packages/can_msgs'...
[12.086s] Listing '/Users/<USER>/Work/drill2/onboard/install/can_msgs/lib/python3.11/site-packages/can_msgs/msg'...
[12.092s] -- Installing: /Users/<USER>/Work/drill2/onboard/install/can_msgs/lib/libcan_msgs__rosidl_generator_py.dylib
[12.214s] -- Up-to-date: /Users/<USER>/Work/drill2/onboard/install/can_msgs/share/can_msgs/cmake/export_can_msgs__rosidl_generator_cExport.cmake
[12.215s] -- Up-to-date: /Users/<USER>/Work/drill2/onboard/install/can_msgs/share/can_msgs/cmake/export_can_msgs__rosidl_generator_cExport-noconfig.cmake
[12.215s] -- Up-to-date: /Users/<USER>/Work/drill2/onboard/install/can_msgs/share/can_msgs/cmake/can_msgs__rosidl_typesupport_introspection_cExport.cmake
[12.215s] -- Up-to-date: /Users/<USER>/Work/drill2/onboard/install/can_msgs/share/can_msgs/cmake/can_msgs__rosidl_typesupport_introspection_cExport-noconfig.cmake
[12.215s] -- Up-to-date: /Users/<USER>/Work/drill2/onboard/install/can_msgs/share/can_msgs/cmake/export_can_msgs__rosidl_typesupport_fastrtps_cExport.cmake
[12.215s] -- Up-to-date: /Users/<USER>/Work/drill2/onboard/install/can_msgs/share/can_msgs/cmake/export_can_msgs__rosidl_typesupport_fastrtps_cExport-noconfig.cmake
[12.215s] -- Up-to-date: /Users/<USER>/Work/drill2/onboard/install/can_msgs/share/can_msgs/cmake/can_msgs__rosidl_typesupport_cExport.cmake
[12.215s] -- Up-to-date: /Users/<USER>/Work/drill2/onboard/install/can_msgs/share/can_msgs/cmake/can_msgs__rosidl_typesupport_cExport-noconfig.cmake
[12.216s] -- Up-to-date: /Users/<USER>/Work/drill2/onboard/install/can_msgs/share/can_msgs/cmake/export_can_msgs__rosidl_generator_cppExport.cmake
[12.216s] -- Up-to-date: /Users/<USER>/Work/drill2/onboard/install/can_msgs/share/can_msgs/cmake/can_msgs__rosidl_typesupport_introspection_cppExport.cmake
[12.216s] -- Up-to-date: /Users/<USER>/Work/drill2/onboard/install/can_msgs/share/can_msgs/cmake/can_msgs__rosidl_typesupport_introspection_cppExport-noconfig.cmake
[12.216s] -- Up-to-date: /Users/<USER>/Work/drill2/onboard/install/can_msgs/share/can_msgs/cmake/export_can_msgs__rosidl_typesupport_fastrtps_cppExport.cmake
[12.216s] -- Up-to-date: /Users/<USER>/Work/drill2/onboard/install/can_msgs/share/can_msgs/cmake/export_can_msgs__rosidl_typesupport_fastrtps_cppExport-noconfig.cmake
[12.216s] -- Up-to-date: /Users/<USER>/Work/drill2/onboard/install/can_msgs/share/can_msgs/cmake/can_msgs__rosidl_typesupport_cppExport.cmake
[12.216s] -- Up-to-date: /Users/<USER>/Work/drill2/onboard/install/can_msgs/share/can_msgs/cmake/can_msgs__rosidl_typesupport_cppExport-noconfig.cmake
[12.216s] -- Up-to-date: /Users/<USER>/Work/drill2/onboard/install/can_msgs/share/can_msgs/cmake/export_can_msgs__rosidl_generator_pyExport.cmake
[12.216s] -- Up-to-date: /Users/<USER>/Work/drill2/onboard/install/can_msgs/share/can_msgs/cmake/export_can_msgs__rosidl_generator_pyExport-noconfig.cmake
[12.218s] Invoked command in '/Users/<USER>/Work/drill2/onboard/build/can_msgs' returned '0': CMAKE_PREFIX_PATH=${CMAKE_PREFIX_PATH}:/Users/<USER>/Work/drill2/onboard/install/remote_connector:/Users/<USER>/Work/drill2/onboard/install/tracks_regulator:/Users/<USER>/Work/drill2/onboard/install/state_tracker:/Users/<USER>/Work/drill2/onboard/install/rtk_connector:/Users/<USER>/Work/drill2/onboard/install/path_follower:/Users/<USER>/Work/drill2/onboard/install/params_server:/Users/<USER>/Work/drill2/onboard/install/leveler:/Users/<USER>/Work/drill2/onboard/install/launchpack:/Users/<USER>/Work/drill2/onboard/install/driller:/Users/<USER>/Work/drill2/onboard/install/depth_tracker:/Users/<USER>/Work/drill2/onboard/install/can_encoder:/Users/<USER>/Work/drill2/onboard/install/can_decoder:/Users/<USER>/Work/drill2/onboard/install/arm_controller:/Users/<USER>/Work/drill2/onboard/install/base_node:/Users/<USER>/ros2_jazzy/install/rosbag2_examples_py:/Users/<USER>/ros2_jazzy/install/launch_testing_examples:/Users/<USER>/ros2_jazzy/install/ros2bag:/Users/<USER>/ros2_jazzy/install/tracetools_test:/Users/<USER>/ros2_jazzy/install/tracetools_launch:/Users/<USER>/ros2_jazzy/install/topic_monitor:/Users/<USER>/ros2_jazzy/install/tf2_tools:/Users/<USER>/ros2_jazzy/install/examples_tf2_py:/Users/<USER>/ros2_jazzy/install/tf2_ros_py:/Users/<USER>/ros2_jazzy/install/examples_rclpy_pointcloud_publisher:/Users/<USER>/ros2_jazzy/install/sensor_msgs_py:/Users/<USER>/ros2_jazzy/install/quality_of_service_demo_py:/Users/<USER>/ros2_jazzy/install/camera_info_manager_py:/Users/<USER>/ros2_jazzy/install/ros2doctor:/Users/<USER>/ros2_jazzy/install/lifecycle_py:/Users/<USER>/ros2_jazzy/install/examples_rclpy_minimal_subscriber:/Users/<USER>/ros2_jazzy/install/examples_rclpy_minimal_service:/Users/<USER>/ros2_jazzy/install/examples_rclpy_minimal_publisher:/Users/<USER>/ros2_jazzy/install/examples_rclpy_minimal_client:/Users/<USER>/ros2_jazzy/install/examples_rclpy_executors:/Users/<USER>/ros2_jazzy/install/demo_nodes_py:/Users/<USER>/ros2_jazzy/install/ros2component:/Users/<USER>/ros2_jazzy/install/sros2:/Users/<USER>/ros2_jazzy/install/ros2trace:/Users/<USER>/ros2_jazzy/install/ros2topic:/Users/<USER>/ros2_jazzy/install/ros2test:/Users/<USER>/ros2_jazzy/install/ros2param:/Users/<USER>/ros2_jazzy/install/ros2lifecycle:/Users/<USER>/ros2_jazzy/install/ros2service:/Users/<USER>/ros2_jazzy/install/ros2run:/Users/<USER>/ros2_jazzy/install/ros2launch:/Users/<USER>/ros2_jazzy/install/ros2pkg:/Users/<USER>/ros2_jazzy/install/ros2node:/Users/<USER>/ros2_jazzy/install/ros2multicast:/Users/<USER>/ros2_jazzy/install/ros2interface:/Users/<USER>/ros2_jazzy/install/ros2action:/Users/<USER>/ros2_jazzy/install/ros2cli:/Users/<USER>/ros2_jazzy/install/launch_testing_ros:/Users/<USER>/ros2_jazzy/install/launch_ros:/Users/<USER>/ros2_jazzy/install/examples_rclpy_minimal_action_server:/Users/<USER>/ros2_jazzy/install/examples_rclpy_minimal_action_client:/Users/<USER>/ros2_jazzy/install/examples_rclpy_guard_conditions:/Users/<USER>/ros2_jazzy/install/action_tutorials_py:/Users/<USER>/ros2_jazzy/install/ament_uncrustify:/Users/<USER>/ros2_jazzy/install/tracetools_trace:/Users/<USER>/ros2_jazzy/install/tracetools_read:/Users/<USER>/ros2_jazzy/install/test_tracetools_launch:/Users/<USER>/ros2_jazzy/install/test_ros2trace:/Users/<USER>/ros2_jazzy/install/test_launch_ros:/Users/<USER>/ros2_jazzy/install/rpyutils:/Users/<USER>/ros2_jazzy/install/rosidl_runtime_py:/Users/<USER>/ros2_jazzy/install/rosidl_pycommon:/Users/<USER>/ros2_jazzy/install/rosidl_cli:/Users/<USER>/ros2_jazzy/install/launch_pytest:/Users/<USER>/ros2_jazzy/install/launch_testing:/Users/<USER>/ros2_jazzy/install/launch_yaml:/Users/<USER>/ros2_jazzy/install/launch_xml:/Users/<USER>/ros2_jazzy/install/launch:/Users/<USER>/ros2_jazzy/install/osrf_pycommon:/Users/<USER>/ros2_jazzy/install/domain_coordinator:/Users/<USER>/ros2_jazzy/install/ament_xmllint:/Users/<USER>/ros2_jazzy/install/ament_pyflakes:/Users/<USER>/ros2_jazzy/install/ament_pycodestyle:/Users/<USER>/ros2_jazzy/install/ament_pep257:/Users/<USER>/ros2_jazzy/install/ament_pclint:/Users/<USER>/ros2_jazzy/install/ament_package:/Users/<USER>/ros2_jazzy/install/ament_mypy:/Users/<USER>/ros2_jazzy/install/ament_lint_cmake:/Users/<USER>/ros2_jazzy/install/ament_flake8:/Users/<USER>/ros2_jazzy/install/ament_copyright:/Users/<USER>/ros2_jazzy/install/ament_lint:/Users/<USER>/ros2_jazzy/install/ament_index_python:/Users/<USER>/ros2_jazzy/install/ament_cpplint:/Users/<USER>/ros2_jazzy/install/ament_cppcheck:/Users/<USER>/ros2_jazzy/install/ament_clang_tidy:/Users/<USER>/ros2_jazzy/install/ament_clang_format SHLVL=2 VIRTUAL_ENV_PROMPT=(.ros2_venv) _=/usr/bin/env /opt/homebrew/bin/cmake --install /Users/<USER>/Work/drill2/onboard/build/can_msgs
