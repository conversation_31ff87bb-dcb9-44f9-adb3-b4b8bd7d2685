[4.217s] Invoking command in '/Users/<USER>/Work/drill2/onboard/build/launchpack': PYTHONPATH=/Users/<USER>/Work/drill2/onboard/build/launchpack/prefix_override:/Users/<USER>/.ros2_venv/lib/python3.11/site-packages/colcon_core/task/python/colcon_distutils_commands:/Users/<USER>/Work/drill2/onboard/install/launchpack/lib/python3.11/site-packages:${PYTHONPATH} SHLVL=2 VIRTUAL_ENV_PROMPT=(.ros2_venv) _=/usr/bin/env /Users/<USER>/.ros2_venv/bin/python3 -W ignore:setup.py install is deprecated -W ignore:easy_install command is deprecated setup.py develop --editable --build-directory /Users/<USER>/Work/drill2/onboard/build/launchpack/build --no-deps symlink_data
[5.127s] running develop
[5.354s] running egg_info
[5.355s] writing launchpack.egg-info/PKG-INFO
[5.355s] writing dependency_links to launchpack.egg-info/dependency_links.txt
[5.355s] writing entry points to launchpack.egg-info/entry_points.txt
[5.355s] writing requirements to launchpack.egg-info/requires.txt
[5.355s] writing top-level names to launchpack.egg-info/top_level.txt
[5.355s] reading manifest file 'launchpack.egg-info/SOURCES.txt'
[5.355s] writing manifest file 'launchpack.egg-info/SOURCES.txt'
[5.443s] running build_ext
[5.443s] Creating /Users/<USER>/Work/drill2/onboard/install/launchpack/lib/python3.11/site-packages/launchpack.egg-link (link to .)
[5.446s] 
[5.446s] Installed /Users/<USER>/Work/drill2/onboard/build/launchpack
[5.446s] running symlink_data
[5.517s] Invoked command in '/Users/<USER>/Work/drill2/onboard/build/launchpack' returned '0': PYTHONPATH=/Users/<USER>/Work/drill2/onboard/build/launchpack/prefix_override:/Users/<USER>/.ros2_venv/lib/python3.11/site-packages/colcon_core/task/python/colcon_distutils_commands:/Users/<USER>/Work/drill2/onboard/install/launchpack/lib/python3.11/site-packages:${PYTHONPATH} SHLVL=2 VIRTUAL_ENV_PROMPT=(.ros2_venv) _=/usr/bin/env /Users/<USER>/.ros2_venv/bin/python3 -W ignore:setup.py install is deprecated -W ignore:easy_install command is deprecated setup.py develop --editable --build-directory /Users/<USER>/Work/drill2/onboard/build/launchpack/build --no-deps symlink_data
