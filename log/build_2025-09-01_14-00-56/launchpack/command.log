Invoking command in '/Users/<USER>/Work/drill2/onboard/build/launchpack': PYTHONPATH=/Users/<USER>/Work/drill2/onboard/build/launchpack/prefix_override:/Users/<USER>/.ros2_venv/lib/python3.11/site-packages/colcon_core/task/python/colcon_distutils_commands:/Users/<USER>/Work/drill2/onboard/install/launchpack/lib/python3.11/site-packages:${PYTHONPATH} SHLVL=2 VIRTUAL_ENV_PROMPT=(.ros2_venv) _=/usr/bin/env /Users/<USER>/.ros2_venv/bin/python3 -W ignore:setup.py install is deprecated -W ignore:easy_install command is deprecated setup.py develop --editable --build-directory /Users/<USER>/Work/drill2/onboard/build/launchpack/build --no-deps symlink_data
Invoked command in '/Users/<USER>/Work/drill2/onboard/build/launchpack' returned '0': PYTHONPATH=/Users/<USER>/Work/drill2/onboard/build/launchpack/prefix_override:/Users/<USER>/.ros2_venv/lib/python3.11/site-packages/colcon_core/task/python/colcon_distutils_commands:/Users/<USER>/Work/drill2/onboard/install/launchpack/lib/python3.11/site-packages:${PYTHONPATH} SHLVL=2 VIRTUAL_ENV_PROMPT=(.ros2_venv) _=/usr/bin/env /Users/<USER>/.ros2_venv/bin/python3 -W ignore:setup.py install is deprecated -W ignore:easy_install command is deprecated setup.py develop --editable --build-directory /Users/<USER>/Work/drill2/onboard/build/launchpack/build --no-deps symlink_data
