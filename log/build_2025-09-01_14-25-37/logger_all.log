[0.329s] DEBUG:colcon:Command line arguments: ['/Users/<USER>/.ros2_venv/bin/colcon', 'build', '--symlink-install', '--cmake-args', '-DCMAKE_CXX_FLAGS=-I/opt/homebrew/include', '--packages-select', 'can_msgs', 'drill_msgs', 'base_node', 'leveler', 'path_follower', 'driller', 'arm_controller', 'state_tracker', 'drill_regulator', 'depth_tracker', 'can_encoder', 'can_decoder', 'params_server', 'rtk_connector', 'tracks_regulator', 'launchpack', 'remote_connector']
[0.329s] DEBUG:colcon:Parsed command line arguments: Namespace(log_base=None, log_level=None, verb_name='build', build_base='build', install_base='install', merge_install=False, symlink_install=True, test_result_base=None, continue_on_error=False, executor='parallel', parallel_workers=8, event_handlers=None, ignore_user_meta=False, metas=['./colcon.meta'], base_paths=['.'], packages_ignore=None, packages_ignore_regex=None, paths=None, packages_up_to=None, packages_up_to_regex=None, packages_above=None, packages_above_and_dependencies=None, packages_above_depth=None, packages_select_by_dep=None, packages_skip_by_dep=None, packages_skip_up_to=None, packages_select_build_failed=False, packages_skip_build_finished=False, packages_select_test_failures=False, packages_skip_test_passed=False, packages_select=['can_msgs', 'drill_msgs', 'base_node', 'leveler', 'path_follower', 'driller', 'arm_controller', 'state_tracker', 'drill_regulator', 'depth_tracker', 'can_encoder', 'can_decoder', 'params_server', 'rtk_connector', 'tracks_regulator', 'launchpack', 'remote_connector'], packages_skip=None, packages_select_regex=None, packages_skip_regex=None, packages_start=None, packages_end=None, cmake_args=['-DCMAKE_CXX_FLAGS=-I/opt/homebrew/include'], cmake_target=None, cmake_target_skip_unavailable=False, cmake_clean_cache=False, cmake_clean_first=False, cmake_force_configure=False, ament_cmake_args=None, catkin_cmake_args=None, catkin_skip_building_tests=False, verb_parser=<colcon_defaults.argument_parser.defaults.DefaultArgumentsDecorator object at 0x102827650>, verb_extension=<colcon_core.verb.build.BuildVerb object at 0x102826d90>, main=<bound method BuildVerb.main of <colcon_core.verb.build.BuildVerb object at 0x102826d90>>)
[0.522s] Level 1:colcon.colcon_core.package_discovery:discover_packages(colcon_meta) check parameters
[0.522s] Level 1:colcon.colcon_core.package_discovery:discover_packages(recursive) check parameters
[0.522s] Level 1:colcon.colcon_core.package_discovery:discover_packages(ignore) check parameters
[0.522s] Level 1:colcon.colcon_core.package_discovery:discover_packages(path) check parameters
[0.522s] Level 1:colcon.colcon_core.package_discovery:discover_packages(colcon_meta) discover
[0.522s] Level 1:colcon.colcon_core.package_discovery:discover_packages(recursive) discover
[0.522s] INFO:colcon.colcon_core.package_discovery:Crawling recursively for packages in '/Users/<USER>/Work/drill2/onboard'
[0.523s] Level 1:colcon.colcon_core.package_identification:_identify(.) by extensions ['ignore', 'ignore_ament_install']
[0.523s] Level 1:colcon.colcon_core.package_identification:_identify(.) by extension 'ignore'
[0.523s] Level 1:colcon.colcon_core.package_identification:_identify(.) by extension 'ignore_ament_install'
[0.523s] Level 1:colcon.colcon_core.package_identification:_identify(.) by extensions ['colcon_pkg']
[0.523s] Level 1:colcon.colcon_core.package_identification:_identify(.) by extension 'colcon_pkg'
[0.523s] Level 1:colcon.colcon_core.package_identification:_identify(.) by extensions ['colcon_meta']
[0.523s] Level 1:colcon.colcon_core.package_identification:_identify(.) by extension 'colcon_meta'
[0.523s] Level 1:colcon.colcon_core.package_identification:_identify(.) by extensions ['ros']
[0.523s] Level 1:colcon.colcon_core.package_identification:_identify(.) by extension 'ros'
[0.534s] Level 1:colcon.colcon_core.package_identification:_identify(.) by extensions ['cmake', 'python']
[0.534s] Level 1:colcon.colcon_core.package_identification:_identify(.) by extension 'cmake'
[0.534s] Level 1:colcon.colcon_core.package_identification:_identify(.) by extension 'python'
[0.535s] Level 1:colcon.colcon_core.package_identification:_identify(.) by extensions ['python_setup_py']
[0.535s] Level 1:colcon.colcon_core.package_identification:_identify(.) by extension 'python_setup_py'
[0.535s] Level 1:colcon.colcon_core.package_identification:_identify(build) by extensions ['ignore', 'ignore_ament_install']
[0.535s] Level 1:colcon.colcon_core.package_identification:_identify(build) by extension 'ignore'
[0.535s] Level 1:colcon.colcon_core.package_identification:_identify(build) ignored
[0.535s] Level 1:colcon.colcon_core.package_identification:_identify(install) by extensions ['ignore', 'ignore_ament_install']
[0.535s] Level 1:colcon.colcon_core.package_identification:_identify(install) by extension 'ignore'
[0.535s] Level 1:colcon.colcon_core.package_identification:_identify(install) ignored
[0.535s] Level 1:colcon.colcon_core.package_identification:_identify(log) by extensions ['ignore', 'ignore_ament_install']
[0.535s] Level 1:colcon.colcon_core.package_identification:_identify(log) by extension 'ignore'
[0.535s] Level 1:colcon.colcon_core.package_identification:_identify(log) ignored
[0.535s] Level 1:colcon.colcon_core.package_identification:_identify(src) by extensions ['ignore', 'ignore_ament_install']
[0.535s] Level 1:colcon.colcon_core.package_identification:_identify(src) by extension 'ignore'
[0.535s] Level 1:colcon.colcon_core.package_identification:_identify(src) by extension 'ignore_ament_install'
[0.535s] Level 1:colcon.colcon_core.package_identification:_identify(src) by extensions ['colcon_pkg']
[0.535s] Level 1:colcon.colcon_core.package_identification:_identify(src) by extension 'colcon_pkg'
[0.535s] Level 1:colcon.colcon_core.package_identification:_identify(src) by extensions ['colcon_meta']
[0.535s] Level 1:colcon.colcon_core.package_identification:_identify(src) by extension 'colcon_meta'
[0.535s] Level 1:colcon.colcon_core.package_identification:_identify(src) by extensions ['ros']
[0.535s] Level 1:colcon.colcon_core.package_identification:_identify(src) by extension 'ros'
[0.535s] Level 1:colcon.colcon_core.package_identification:_identify(src) by extensions ['cmake', 'python']
[0.535s] Level 1:colcon.colcon_core.package_identification:_identify(src) by extension 'cmake'
[0.535s] Level 1:colcon.colcon_core.package_identification:_identify(src) by extension 'python'
[0.535s] Level 1:colcon.colcon_core.package_identification:_identify(src) by extensions ['python_setup_py']
[0.535s] Level 1:colcon.colcon_core.package_identification:_identify(src) by extension 'python_setup_py'
[0.536s] Level 1:colcon.colcon_core.package_identification:_identify(src/arm_controller) by extensions ['ignore', 'ignore_ament_install']
[0.536s] Level 1:colcon.colcon_core.package_identification:_identify(src/arm_controller) by extension 'ignore'
[0.536s] Level 1:colcon.colcon_core.package_identification:_identify(src/arm_controller) by extension 'ignore_ament_install'
[0.536s] Level 1:colcon.colcon_core.package_identification:_identify(src/arm_controller) by extensions ['colcon_pkg']
[0.536s] Level 1:colcon.colcon_core.package_identification:_identify(src/arm_controller) by extension 'colcon_pkg'
[0.536s] Level 1:colcon.colcon_core.package_identification:_identify(src/arm_controller) by extensions ['colcon_meta']
[0.536s] Level 1:colcon.colcon_core.package_identification:_identify(src/arm_controller) by extension 'colcon_meta'
[0.536s] Level 1:colcon.colcon_core.package_identification:_identify(src/arm_controller) by extensions ['ros']
[0.536s] Level 1:colcon.colcon_core.package_identification:_identify(src/arm_controller) by extension 'ros'
[0.538s] DEBUG:colcon.colcon_core.package_identification:Package 'src/arm_controller' with type 'ros.ament_python' and name 'arm_controller'
[0.538s] Level 1:colcon.colcon_core.package_identification:_identify(src/base_node) by extensions ['ignore', 'ignore_ament_install']
[0.538s] Level 1:colcon.colcon_core.package_identification:_identify(src/base_node) by extension 'ignore'
[0.538s] Level 1:colcon.colcon_core.package_identification:_identify(src/base_node) by extension 'ignore_ament_install'
[0.538s] Level 1:colcon.colcon_core.package_identification:_identify(src/base_node) by extensions ['colcon_pkg']
[0.538s] Level 1:colcon.colcon_core.package_identification:_identify(src/base_node) by extension 'colcon_pkg'
[0.538s] Level 1:colcon.colcon_core.package_identification:_identify(src/base_node) by extensions ['colcon_meta']
[0.538s] Level 1:colcon.colcon_core.package_identification:_identify(src/base_node) by extension 'colcon_meta'
[0.538s] Level 1:colcon.colcon_core.package_identification:_identify(src/base_node) by extensions ['ros']
[0.538s] Level 1:colcon.colcon_core.package_identification:_identify(src/base_node) by extension 'ros'
[0.539s] DEBUG:colcon.colcon_core.package_identification:Package 'src/base_node' with type 'ros.ament_python' and name 'base_node'
[0.539s] Level 1:colcon.colcon_core.package_identification:_identify(src/can_decoder) by extensions ['ignore', 'ignore_ament_install']
[0.539s] Level 1:colcon.colcon_core.package_identification:_identify(src/can_decoder) by extension 'ignore'
[0.539s] Level 1:colcon.colcon_core.package_identification:_identify(src/can_decoder) by extension 'ignore_ament_install'
[0.539s] Level 1:colcon.colcon_core.package_identification:_identify(src/can_decoder) by extensions ['colcon_pkg']
[0.539s] Level 1:colcon.colcon_core.package_identification:_identify(src/can_decoder) by extension 'colcon_pkg'
[0.539s] Level 1:colcon.colcon_core.package_identification:_identify(src/can_decoder) by extensions ['colcon_meta']
[0.539s] Level 1:colcon.colcon_core.package_identification:_identify(src/can_decoder) by extension 'colcon_meta'
[0.539s] Level 1:colcon.colcon_core.package_identification:_identify(src/can_decoder) by extensions ['ros']
[0.539s] Level 1:colcon.colcon_core.package_identification:_identify(src/can_decoder) by extension 'ros'
[0.540s] DEBUG:colcon.colcon_core.package_identification:Package 'src/can_decoder' with type 'ros.ament_python' and name 'can_decoder'
[0.540s] Level 1:colcon.colcon_core.package_identification:_identify(src/can_encoder) by extensions ['ignore', 'ignore_ament_install']
[0.540s] Level 1:colcon.colcon_core.package_identification:_identify(src/can_encoder) by extension 'ignore'
[0.540s] Level 1:colcon.colcon_core.package_identification:_identify(src/can_encoder) by extension 'ignore_ament_install'
[0.540s] Level 1:colcon.colcon_core.package_identification:_identify(src/can_encoder) by extensions ['colcon_pkg']
[0.540s] Level 1:colcon.colcon_core.package_identification:_identify(src/can_encoder) by extension 'colcon_pkg'
[0.540s] Level 1:colcon.colcon_core.package_identification:_identify(src/can_encoder) by extensions ['colcon_meta']
[0.540s] Level 1:colcon.colcon_core.package_identification:_identify(src/can_encoder) by extension 'colcon_meta'
[0.540s] Level 1:colcon.colcon_core.package_identification:_identify(src/can_encoder) by extensions ['ros']
[0.540s] Level 1:colcon.colcon_core.package_identification:_identify(src/can_encoder) by extension 'ros'
[0.541s] DEBUG:colcon.colcon_core.package_identification:Package 'src/can_encoder' with type 'ros.ament_python' and name 'can_encoder'
[0.541s] Level 1:colcon.colcon_core.package_identification:_identify(src/can_msgs) by extensions ['ignore', 'ignore_ament_install']
[0.541s] Level 1:colcon.colcon_core.package_identification:_identify(src/can_msgs) by extension 'ignore'
[0.541s] Level 1:colcon.colcon_core.package_identification:_identify(src/can_msgs) by extension 'ignore_ament_install'
[0.541s] Level 1:colcon.colcon_core.package_identification:_identify(src/can_msgs) by extensions ['colcon_pkg']
[0.541s] Level 1:colcon.colcon_core.package_identification:_identify(src/can_msgs) by extension 'colcon_pkg'
[0.541s] Level 1:colcon.colcon_core.package_identification:_identify(src/can_msgs) by extensions ['colcon_meta']
[0.541s] Level 1:colcon.colcon_core.package_identification:_identify(src/can_msgs) by extension 'colcon_meta'
[0.541s] Level 1:colcon.colcon_core.package_identification:_identify(src/can_msgs) by extensions ['ros']
[0.541s] Level 1:colcon.colcon_core.package_identification:_identify(src/can_msgs) by extension 'ros'
[0.542s] DEBUG:colcon.colcon_core.package_identification:Package 'src/can_msgs' with type 'ros.ament_cmake' and name 'can_msgs'
[0.542s] Level 1:colcon.colcon_core.package_identification:_identify(src/depth_tracker) by extensions ['ignore', 'ignore_ament_install']
[0.542s] Level 1:colcon.colcon_core.package_identification:_identify(src/depth_tracker) by extension 'ignore'
[0.542s] Level 1:colcon.colcon_core.package_identification:_identify(src/depth_tracker) by extension 'ignore_ament_install'
[0.542s] Level 1:colcon.colcon_core.package_identification:_identify(src/depth_tracker) by extensions ['colcon_pkg']
[0.542s] Level 1:colcon.colcon_core.package_identification:_identify(src/depth_tracker) by extension 'colcon_pkg'
[0.542s] Level 1:colcon.colcon_core.package_identification:_identify(src/depth_tracker) by extensions ['colcon_meta']
[0.542s] Level 1:colcon.colcon_core.package_identification:_identify(src/depth_tracker) by extension 'colcon_meta'
[0.542s] Level 1:colcon.colcon_core.package_identification:_identify(src/depth_tracker) by extensions ['ros']
[0.542s] Level 1:colcon.colcon_core.package_identification:_identify(src/depth_tracker) by extension 'ros'
[0.542s] DEBUG:colcon.colcon_core.package_identification:Package 'src/depth_tracker' with type 'ros.ament_python' and name 'depth_tracker'
[0.543s] Level 1:colcon.colcon_core.package_identification:_identify(src/docker) by extensions ['ignore', 'ignore_ament_install']
[0.543s] Level 1:colcon.colcon_core.package_identification:_identify(src/docker) by extension 'ignore'
[0.543s] Level 1:colcon.colcon_core.package_identification:_identify(src/docker) by extension 'ignore_ament_install'
[0.543s] Level 1:colcon.colcon_core.package_identification:_identify(src/docker) by extensions ['colcon_pkg']
[0.543s] Level 1:colcon.colcon_core.package_identification:_identify(src/docker) by extension 'colcon_pkg'
[0.543s] Level 1:colcon.colcon_core.package_identification:_identify(src/docker) by extensions ['colcon_meta']
[0.543s] Level 1:colcon.colcon_core.package_identification:_identify(src/docker) by extension 'colcon_meta'
[0.543s] Level 1:colcon.colcon_core.package_identification:_identify(src/docker) by extensions ['ros']
[0.543s] Level 1:colcon.colcon_core.package_identification:_identify(src/docker) by extension 'ros'
[0.543s] Level 1:colcon.colcon_core.package_identification:_identify(src/docker) by extensions ['cmake', 'python']
[0.543s] Level 1:colcon.colcon_core.package_identification:_identify(src/docker) by extension 'cmake'
[0.543s] Level 1:colcon.colcon_core.package_identification:_identify(src/docker) by extension 'python'
[0.543s] Level 1:colcon.colcon_core.package_identification:_identify(src/docker) by extensions ['python_setup_py']
[0.543s] Level 1:colcon.colcon_core.package_identification:_identify(src/docker) by extension 'python_setup_py'
[0.543s] Level 1:colcon.colcon_core.package_identification:_identify(src/drill_msgs) by extensions ['ignore', 'ignore_ament_install']
[0.543s] Level 1:colcon.colcon_core.package_identification:_identify(src/drill_msgs) by extension 'ignore'
[0.543s] Level 1:colcon.colcon_core.package_identification:_identify(src/drill_msgs) by extension 'ignore_ament_install'
[0.543s] Level 1:colcon.colcon_core.package_identification:_identify(src/drill_msgs) by extensions ['colcon_pkg']
[0.543s] Level 1:colcon.colcon_core.package_identification:_identify(src/drill_msgs) by extension 'colcon_pkg'
[0.543s] Level 1:colcon.colcon_core.package_identification:_identify(src/drill_msgs) by extensions ['colcon_meta']
[0.543s] Level 1:colcon.colcon_core.package_identification:_identify(src/drill_msgs) by extension 'colcon_meta'
[0.543s] Level 1:colcon.colcon_core.package_identification:_identify(src/drill_msgs) by extensions ['ros']
[0.543s] Level 1:colcon.colcon_core.package_identification:_identify(src/drill_msgs) by extension 'ros'
[0.544s] DEBUG:colcon.colcon_core.package_identification:Package 'src/drill_msgs' with type 'ros.ament_cmake' and name 'drill_msgs'
[0.544s] Level 1:colcon.colcon_core.package_identification:_identify(src/drill_regulator) by extensions ['ignore', 'ignore_ament_install']
[0.544s] Level 1:colcon.colcon_core.package_identification:_identify(src/drill_regulator) by extension 'ignore'
[0.544s] Level 1:colcon.colcon_core.package_identification:_identify(src/drill_regulator) by extension 'ignore_ament_install'
[0.544s] Level 1:colcon.colcon_core.package_identification:_identify(src/drill_regulator) by extensions ['colcon_pkg']
[0.544s] Level 1:colcon.colcon_core.package_identification:_identify(src/drill_regulator) by extension 'colcon_pkg'
[0.544s] Level 1:colcon.colcon_core.package_identification:_identify(src/drill_regulator) by extensions ['colcon_meta']
[0.544s] Level 1:colcon.colcon_core.package_identification:_identify(src/drill_regulator) by extension 'colcon_meta'
[0.544s] Level 1:colcon.colcon_core.package_identification:_identify(src/drill_regulator) by extensions ['ros']
[0.544s] Level 1:colcon.colcon_core.package_identification:_identify(src/drill_regulator) by extension 'ros'
[0.545s] DEBUG:colcon.colcon_core.package_identification:Failed to parse potential ROS package manifest in'src/drill_regulator': Error(s) in package 'src/drill_regulator/package.xml':
The manifest contains invalid XML:
not well-formed (invalid token): line 1, column 16
[0.545s] Level 1:colcon.colcon_core.package_identification:_identify(src/drill_regulator) by extensions ['cmake', 'python']
[0.545s] Level 1:colcon.colcon_core.package_identification:_identify(src/drill_regulator) by extension 'cmake'
[0.545s] Level 1:colcon.colcon_core.package_identification:_identify(src/drill_regulator) by extension 'python'
[0.545s] DEBUG:colcon.colcon_core.package_identification:Python package in 'src/drill_regulator' passes arguments to the setup() function which requires a different identification extension than 'python'
[0.545s] Level 1:colcon.colcon_core.package_identification:_identify(src/drill_regulator) by extensions ['python_setup_py']
[0.545s] Level 1:colcon.colcon_core.package_identification:_identify(src/drill_regulator) by extension 'python_setup_py'
