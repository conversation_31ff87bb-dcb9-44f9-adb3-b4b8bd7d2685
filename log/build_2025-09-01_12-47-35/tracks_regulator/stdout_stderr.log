running develop
running egg_info
writing tracks_regulator.egg-info/PKG-INFO
writing dependency_links to tracks_regulator.egg-info/dependency_links.txt
writing entry points to tracks_regulator.egg-info/entry_points.txt
writing requirements to tracks_regulator.egg-info/requires.txt
writing top-level names to tracks_regulator.egg-info/top_level.txt
reading manifest file 'tracks_regulator.egg-info/SOURCES.txt'
writing manifest file 'tracks_regulator.egg-info/SOURCES.txt'
running build_ext
Creating /Users/<USER>/Work/drill2/onboard/install/tracks_regulator/lib/python3.11/site-packages/tracks-regulator.egg-link (link to .)
Installing test_node script to /Users/<USER>/Work/drill2/onboard/install/tracks_regulator/lib/tracks_regulator
Installing tracks_regulator_node script to /Users/<USER>/Work/drill2/onboard/install/tracks_regulator/lib/tracks_regulator

Installed /Users/<USER>/Work/drill2/onboard/build/tracks_regulator
running symlink_data
