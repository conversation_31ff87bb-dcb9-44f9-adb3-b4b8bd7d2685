running develop
running egg_info
writing arm_controller.egg-info/PKG-INFO
writing dependency_links to arm_controller.egg-info/dependency_links.txt
writing entry points to arm_controller.egg-info/entry_points.txt
writing requirements to arm_controller.egg-info/requires.txt
writing top-level names to arm_controller.egg-info/top_level.txt
reading manifest file 'arm_controller.egg-info/SOURCES.txt'
writing manifest file 'arm_controller.egg-info/SOURCES.txt'
running build_ext
Creating /Users/<USER>/Work/drill2/onboard/install/arm_controller/lib/python3.11/site-packages/arm-controller.egg-link (link to .)
Installing arm_controller script to /Users/<USER>/Work/drill2/onboard/install/arm_controller/lib/arm_controller

Installed /Users/<USER>/Work/drill2/onboard/build/arm_controller
running symlink_data
