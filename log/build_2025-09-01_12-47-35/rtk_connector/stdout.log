running develop
running egg_info
writing rtk_connector.egg-info/PKG-INFO
writing dependency_links to rtk_connector.egg-info/dependency_links.txt
writing entry points to rtk_connector.egg-info/entry_points.txt
writing requirements to rtk_connector.egg-info/requires.txt
writing top-level names to rtk_connector.egg-info/top_level.txt
reading manifest file 'rtk_connector.egg-info/SOURCES.txt'
writing manifest file 'rtk_connector.egg-info/SOURCES.txt'
running build_ext
Creating /Users/<USER>/Work/drill2/onboard/install/rtk_connector/lib/python3.11/site-packages/rtk-connector.egg-link (link to .)
Installing rtk_connector script to /Users/<USER>/Work/drill2/onboard/install/rtk_connector/lib/rtk_connector

Installed /Users/<USER>/Work/drill2/onboard/build/rtk_connector
running symlink_data
