[1.439s] Invoking command in '/Users/<USER>/Work/drill2/onboard/build/drill_regulator': PYTHONPATH=/Users/<USER>/Work/drill2/onboard/build/drill_regulator/prefix_override:/Users/<USER>/.ros2_venv/lib/python3.11/site-packages/colcon_core/task/python/colcon_distutils_commands:/Users/<USER>/Work/drill2/onboard/install/drill_regulator/lib/python3.11/site-packages:${PYTHONPATH} SHLVL=2 VIRTUAL_ENV_PROMPT=(.ros2_venv) _=/usr/bin/env /Users/<USER>/.ros2_venv/bin/python3 -W ignore:setup.py install is deprecated -W ignore:easy_install command is deprecated setup.py develop --editable --build-directory /Users/<USER>/Work/drill2/onboard/build/drill_regulator/build --no-deps symlink_data
[1.924s] running develop
[2.081s] running egg_info
[2.081s] writing drill_regulator.egg-info/PKG-INFO
[2.082s] writing dependency_links to drill_regulator.egg-info/dependency_links.txt
[2.082s] writing entry points to drill_regulator.egg-info/entry_points.txt
[2.083s] writing requirements to drill_regulator.egg-info/requires.txt
[2.083s] writing top-level names to drill_regulator.egg-info/top_level.txt
[2.083s] reading manifest file 'drill_regulator.egg-info/SOURCES.txt'
[2.083s] writing manifest file 'drill_regulator.egg-info/SOURCES.txt'
[2.133s] running build_ext
[2.133s] Creating /Users/<USER>/Work/drill2/onboard/install/drill_regulator/lib/python3.11/site-packages/drill-regulator.egg-link (link to .)
[2.133s] Installing drill_regulator_node script to /Users/<USER>/Work/drill2/onboard/install/drill_regulator/lib/drill_regulator
[2.134s] 
[2.134s] Installed /Users/<USER>/Work/drill2/onboard/build/drill_regulator
[2.134s] running symlink_data
[2.158s] Invoked command in '/Users/<USER>/Work/drill2/onboard/build/drill_regulator' returned '0': PYTHONPATH=/Users/<USER>/Work/drill2/onboard/build/drill_regulator/prefix_override:/Users/<USER>/.ros2_venv/lib/python3.11/site-packages/colcon_core/task/python/colcon_distutils_commands:/Users/<USER>/Work/drill2/onboard/install/drill_regulator/lib/python3.11/site-packages:${PYTHONPATH} SHLVL=2 VIRTUAL_ENV_PROMPT=(.ros2_venv) _=/usr/bin/env /Users/<USER>/.ros2_venv/bin/python3 -W ignore:setup.py install is deprecated -W ignore:easy_install command is deprecated setup.py develop --editable --build-directory /Users/<USER>/Work/drill2/onboard/build/drill_regulator/build --no-deps symlink_data
