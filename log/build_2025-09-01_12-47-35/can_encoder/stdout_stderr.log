running develop
running egg_info
writing can_encoder.egg-info/PKG-INFO
writing dependency_links to can_encoder.egg-info/dependency_links.txt
writing entry points to can_encoder.egg-info/entry_points.txt
writing requirements to can_encoder.egg-info/requires.txt
writing top-level names to can_encoder.egg-info/top_level.txt
reading manifest file 'can_encoder.egg-info/SOURCES.txt'
writing manifest file 'can_encoder.egg-info/SOURCES.txt'
running build_ext
Creating /Users/<USER>/Work/drill2/onboard/install/can_encoder/lib/python3.11/site-packages/can-encoder.egg-link (link to .)
Installing can_encoder script to /Users/<USER>/Work/drill2/onboard/install/can_encoder/lib/can_encoder

Installed /Users/<USER>/Work/drill2/onboard/build/can_encoder
running symlink_data
