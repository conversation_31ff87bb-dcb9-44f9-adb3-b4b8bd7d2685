// generated from rosidl_typesupport_introspection_c/resource/idl__type_support.c.em
// with input from drill_msgs:msg/ForkState.idl
// generated code does not contain a copyright notice

#include <stddef.h>
#include "drill_msgs/msg/detail/fork_state__rosidl_typesupport_introspection_c.h"
#include "drill_msgs/msg/rosidl_typesupport_introspection_c__visibility_control.h"
#include "rosidl_typesupport_introspection_c/field_types.h"
#include "rosidl_typesupport_introspection_c/identifier.h"
#include "rosidl_typesupport_introspection_c/message_introspection.h"
#include "drill_msgs/msg/detail/fork_state__functions.h"
#include "drill_msgs/msg/detail/fork_state__struct.h"


// Include directives for member types
// Member `header`
#include "std_msgs/msg/header.h"
// Member `header`
#include "std_msgs/msg/detail/header__rosidl_typesupport_introspection_c.h"

#ifdef __cplusplus
extern "C"
{
#endif

void drill_msgs__msg__ForkState__rosidl_typesupport_introspection_c__ForkState_init_function(
  void * message_memory, enum rosidl_runtime_c__message_initialization _init)
{
  // TODO(karsten1987): initializers are not yet implemented for typesupport c
  // see https://github.com/ros2/ros2/issues/397
  (void) _init;
  drill_msgs__msg__ForkState__init(message_memory);
}

void drill_msgs__msg__ForkState__rosidl_typesupport_introspection_c__ForkState_fini_function(void * message_memory)
{
  drill_msgs__msg__ForkState__fini(message_memory);
}

static rosidl_typesupport_introspection_c__MessageMember drill_msgs__msg__ForkState__rosidl_typesupport_introspection_c__ForkState_message_member_array[5] = {
  {
    "header",  // name
    rosidl_typesupport_introspection_c__ROS_TYPE_MESSAGE,  // type
    0,  // upper bound of string
    NULL,  // members of sub message (initialized later)
    false,  // is key
    false,  // is array
    0,  // array size
    false,  // is upper bound
    offsetof(drill_msgs__msg__ForkState, header),  // bytes offset in struct
    NULL,  // default value
    NULL,  // size() function pointer
    NULL,  // get_const(index) function pointer
    NULL,  // get(index) function pointer
    NULL,  // fetch(index, &value) function pointer
    NULL,  // assign(index, value) function pointer
    NULL  // resize(index) function pointer
  },
  {
    "extended",  // name
    rosidl_typesupport_introspection_c__ROS_TYPE_BOOLEAN,  // type
    0,  // upper bound of string
    NULL,  // members of sub message
    false,  // is key
    false,  // is array
    0,  // array size
    false,  // is upper bound
    offsetof(drill_msgs__msg__ForkState, extended),  // bytes offset in struct
    NULL,  // default value
    NULL,  // size() function pointer
    NULL,  // get_const(index) function pointer
    NULL,  // get(index) function pointer
    NULL,  // fetch(index, &value) function pointer
    NULL,  // assign(index, value) function pointer
    NULL  // resize(index) function pointer
  },
  {
    "retracted",  // name
    rosidl_typesupport_introspection_c__ROS_TYPE_BOOLEAN,  // type
    0,  // upper bound of string
    NULL,  // members of sub message
    false,  // is key
    false,  // is array
    0,  // array size
    false,  // is upper bound
    offsetof(drill_msgs__msg__ForkState, retracted),  // bytes offset in struct
    NULL,  // default value
    NULL,  // size() function pointer
    NULL,  // get_const(index) function pointer
    NULL,  // get(index) function pointer
    NULL,  // fetch(index, &value) function pointer
    NULL,  // assign(index, value) function pointer
    NULL  // resize(index) function pointer
  },
  {
    "cw_limit",  // name
    rosidl_typesupport_introspection_c__ROS_TYPE_BOOLEAN,  // type
    0,  // upper bound of string
    NULL,  // members of sub message
    false,  // is key
    false,  // is array
    0,  // array size
    false,  // is upper bound
    offsetof(drill_msgs__msg__ForkState, cw_limit),  // bytes offset in struct
    NULL,  // default value
    NULL,  // size() function pointer
    NULL,  // get_const(index) function pointer
    NULL,  // get(index) function pointer
    NULL,  // fetch(index, &value) function pointer
    NULL,  // assign(index, value) function pointer
    NULL  // resize(index) function pointer
  },
  {
    "ccw_limit",  // name
    rosidl_typesupport_introspection_c__ROS_TYPE_BOOLEAN,  // type
    0,  // upper bound of string
    NULL,  // members of sub message
    false,  // is key
    false,  // is array
    0,  // array size
    false,  // is upper bound
    offsetof(drill_msgs__msg__ForkState, ccw_limit),  // bytes offset in struct
    NULL,  // default value
    NULL,  // size() function pointer
    NULL,  // get_const(index) function pointer
    NULL,  // get(index) function pointer
    NULL,  // fetch(index, &value) function pointer
    NULL,  // assign(index, value) function pointer
    NULL  // resize(index) function pointer
  }
};

static const rosidl_typesupport_introspection_c__MessageMembers drill_msgs__msg__ForkState__rosidl_typesupport_introspection_c__ForkState_message_members = {
  "drill_msgs__msg",  // message namespace
  "ForkState",  // message name
  5,  // number of fields
  sizeof(drill_msgs__msg__ForkState),
  false,  // has_any_key_member_
  drill_msgs__msg__ForkState__rosidl_typesupport_introspection_c__ForkState_message_member_array,  // message members
  drill_msgs__msg__ForkState__rosidl_typesupport_introspection_c__ForkState_init_function,  // function to initialize message memory (memory has to be allocated)
  drill_msgs__msg__ForkState__rosidl_typesupport_introspection_c__ForkState_fini_function  // function to terminate message instance (will not free memory)
};

// this is not const since it must be initialized on first access
// since C does not allow non-integral compile-time constants
static rosidl_message_type_support_t drill_msgs__msg__ForkState__rosidl_typesupport_introspection_c__ForkState_message_type_support_handle = {
  0,
  &drill_msgs__msg__ForkState__rosidl_typesupport_introspection_c__ForkState_message_members,
  get_message_typesupport_handle_function,
  &drill_msgs__msg__ForkState__get_type_hash,
  &drill_msgs__msg__ForkState__get_type_description,
  &drill_msgs__msg__ForkState__get_type_description_sources,
};

ROSIDL_TYPESUPPORT_INTROSPECTION_C_EXPORT_drill_msgs
const rosidl_message_type_support_t *
ROSIDL_TYPESUPPORT_INTERFACE__MESSAGE_SYMBOL_NAME(rosidl_typesupport_introspection_c, drill_msgs, msg, ForkState)() {
  drill_msgs__msg__ForkState__rosidl_typesupport_introspection_c__ForkState_message_member_array[0].members_ =
    ROSIDL_TYPESUPPORT_INTERFACE__MESSAGE_SYMBOL_NAME(rosidl_typesupport_introspection_c, std_msgs, msg, Header)();
  if (!drill_msgs__msg__ForkState__rosidl_typesupport_introspection_c__ForkState_message_type_support_handle.typesupport_identifier) {
    drill_msgs__msg__ForkState__rosidl_typesupport_introspection_c__ForkState_message_type_support_handle.typesupport_identifier =
      rosidl_typesupport_introspection_c__identifier;
  }
  return &drill_msgs__msg__ForkState__rosidl_typesupport_introspection_c__ForkState_message_type_support_handle;
}
#ifdef __cplusplus
}
#endif
