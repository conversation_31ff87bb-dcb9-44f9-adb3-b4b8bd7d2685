{"package_name": "drill_msgs", "non_idl_tuples": ["/Users/<USER>/Work/drill2/onboard/src/drill_msgs:msg/Event.msg", "/Users/<USER>/Work/drill2/onboard/src/drill_msgs:msg/Report.msg", "/Users/<USER>/Work/drill2/onboard/src/drill_msgs:msg/Permission.msg", "/Users/<USER>/Work/drill2/onboard/src/drill_msgs:msg/ParamNotification.msg", "/Users/<USER>/Work/drill2/onboard/src/drill_msgs:msg/BoolStamped.msg", "/Users/<USER>/Work/drill2/onboard/src/drill_msgs:msg/Vector2d.msg", "/Users/<USER>/Work/drill2/onboard/src/drill_msgs:msg/OpenCloseAction.msg", "/Users/<USER>/Work/drill2/onboard/src/drill_msgs:msg/IMU.msg", "/Users/<USER>/Work/drill2/onboard/src/drill_msgs:msg/EngineState.msg", "/Users/<USER>/Work/drill2/onboard/src/drill_msgs:msg/DrillStateRaw.msg", "/Users/<USER>/Work/drill2/onboard/src/drill_msgs:msg/DrillState.msg", "/Users/<USER>/Work/drill2/onboard/src/drill_msgs:msg/DepthInfo.msg", "/Users/<USER>/Work/drill2/onboard/src/drill_msgs:msg/FloatStamped.msg", "/Users/<USER>/Work/drill2/onboard/src/drill_msgs:msg/JacksStateRaw.msg", "/Users/<USER>/Work/drill2/onboard/src/drill_msgs:msg/JacksSwitchState.msg", "/Users/<USER>/Work/drill2/onboard/src/drill_msgs:msg/JacksSwitchStateRaw.msg", "/Users/<USER>/Work/drill2/onboard/src/drill_msgs:msg/PinsStateRaw.msg", "/Users/<USER>/Work/drill2/onboard/src/drill_msgs:msg/ArmStateRaw.msg", "/Users/<USER>/Work/drill2/onboard/src/drill_msgs:msg/ArmState.msg", "/Users/<USER>/Work/drill2/onboard/src/drill_msgs:msg/ForkStateRaw.msg", "/Users/<USER>/Work/drill2/onboard/src/drill_msgs:msg/ForkState.msg", "/Users/<USER>/Work/drill2/onboard/src/drill_msgs:msg/CarouselStateRaw.msg", "/Users/<USER>/Work/drill2/onboard/src/drill_msgs:msg/WrenchStateRaw.msg", "/Users/<USER>/Work/drill2/onboard/src/drill_msgs:msg/DustFlapsState.msg", "/Users/<USER>/Work/drill2/onboard/src/drill_msgs:msg/StateMachineStatus.msg", "/Users/<USER>/Work/drill2/onboard/src/drill_msgs:msg/StateCommand.msg", "/Users/<USER>/Work/drill2/onboard/src/drill_msgs:msg/RmoHealth.msg", "/Users/<USER>/Work/drill2/onboard/src/drill_msgs:msg/CarouselCtrl.msg", "/Users/<USER>/Work/drill2/onboard/src/drill_msgs:msg/TowerCtrl.msg", "/Users/<USER>/Work/drill2/onboard/src/drill_msgs:msg/WrenchCtrl.msg", "/Users/<USER>/Work/drill2/onboard/src/drill_msgs:msg/TracksCtrl.msg", "/Users/<USER>/Work/drill2/onboard/src/drill_msgs:msg/FloatCtrl.msg", "/Users/<USER>/Work/drill2/onboard/src/drill_msgs:msg/DrillCtrl.msg", "/Users/<USER>/Work/drill2/onboard/src/drill_msgs:msg/DrillActuatorCtrl.msg", "/Users/<USER>/Work/drill2/onboard/src/drill_msgs:msg/AirCtrl.msg", "/Users/<USER>/Work/drill2/onboard/src/drill_msgs:msg/JacksCtrl.msg", "/Users/<USER>/Work/drill2/onboard/src/drill_msgs:msg/UpsStatus.msg", "/Users/<USER>/Work/drill2/onboard/src/drill_msgs:msg/LampCtrl.msg", "/Users/<USER>/Work/drill2/onboard/src/drill_msgs:msg/Level.msg", "/Users/<USER>/Work/drill2/onboard/src/drill_msgs:msg/GNSS.msg", "/Users/<USER>/Work/drill2/onboard/src/drill_msgs:msg/Position.msg", "/Users/<USER>/Work/drill2/onboard/src/drill_msgs:msg/SpeedState.msg", "/Users/<USER>/Work/drill2/onboard/src/drill_msgs:msg/TracksState.msg", "/Users/<USER>/Work/drill2/onboard/src/drill_msgs:msg/TowerState.msg", "/Users/<USER>/Work/drill2/onboard/src/drill_msgs:msg/ModeCtrl.msg", "/Users/<USER>/Work/drill2/onboard/src/drill_msgs:msg/Point2d.msg", "/Users/<USER>/Work/drill2/onboard/src/drill_msgs:msg/Path.msg", "/Users/<USER>/Work/drill2/onboard/src/drill_msgs:msg/PathPoint.msg", "/Users/<USER>/Work/drill2/onboard/src/drill_msgs:msg/MainAction.msg", "/Users/<USER>/Work/drill2/onboard/src/drill_msgs:msg/DrillerAction.msg", "/Users/<USER>/Work/drill2/onboard/src/drill_msgs:msg/RockType.msg", "/Users/<USER>/Work/drill2/onboard/src/drill_msgs:msg/DriveAction.msg", "/Users/<USER>/Work/drill2/onboard/src/drill_msgs:msg/DriveStatus.msg", "/Users/<USER>/Work/drill2/onboard/src/drill_msgs:srv/GetCurrentDriveAction.srv"]}