{"backtrace": 2, "backtraceGraph": {"commands": ["add_custom_target", "rosidl_generate_interfaces", "add_dependencies", "include", "ament_execute_extensions"], "files": ["/Users/<USER>/ros2_jazzy/install/rosidl_cmake/share/rosidl_cmake/cmake/rosidl_generate_interfaces.cmake", "CMakeLists.txt", "/Users/<USER>/ros2_jazzy/install/rosidl_generator_type_description/share/rosidl_generator_type_description/cmake/rosidl_generator_type_description_generate_interfaces.cmake", "/Users/<USER>/ros2_jazzy/install/ament_cmake_core/share/ament_cmake_core/cmake/core/ament_execute_extensions.cmake", "/Users/<USER>/ros2_jazzy/install/rosidl_generator_c/share/rosidl_generator_c/cmake/rosidl_generator_c_generate_interfaces.cmake", "/Users/<USER>/ros2_jazzy/install/rosidl_typesupport_fastrtps_c/share/rosidl_typesupport_fastrtps_c/cmake/rosidl_typesupport_fastrtps_c_generate_interfaces.cmake", "/Users/<USER>/ros2_jazzy/install/rosidl_typesupport_fastrtps_cpp/share/rosidl_typesupport_fastrtps_cpp/cmake/rosidl_typesupport_fastrtps_cpp_generate_interfaces.cmake", "/Users/<USER>/ros2_jazzy/install/rosidl_typesupport_introspection_c/share/rosidl_typesupport_introspection_c/cmake/rosidl_typesupport_introspection_c_generate_interfaces.cmake", "/Users/<USER>/ros2_jazzy/install/rosidl_typesupport_c/share/rosidl_typesupport_c/cmake/rosidl_typesupport_c_generate_interfaces.cmake", "/Users/<USER>/ros2_jazzy/install/rosidl_typesupport_introspection_cpp/share/rosidl_typesupport_introspection_cpp/cmake/rosidl_typesupport_introspection_cpp_generate_interfaces.cmake", "/Users/<USER>/ros2_jazzy/install/rosidl_generator_cpp/share/rosidl_generator_cpp/cmake/rosidl_generator_cpp_generate_interfaces.cmake", "/Users/<USER>/ros2_jazzy/install/rosidl_typesupport_cpp/share/rosidl_typesupport_cpp/cmake/rosidl_typesupport_cpp_generate_interfaces.cmake"], "nodes": [{"file": 1}, {"command": 1, "file": 1, "line": 17, "parent": 0}, {"command": 0, "file": 0, "line": 207, "parent": 1}, {"command": 4, "file": 0, "line": 280, "parent": 1}, {"command": 3, "file": 3, "line": 48, "parent": 3}, {"file": 2, "parent": 4}, {"command": 2, "file": 2, "line": 97, "parent": 5}, {"command": 3, "file": 3, "line": 48, "parent": 3}, {"file": 4, "parent": 7}, {"command": 2, "file": 4, "line": 179, "parent": 8}, {"command": 3, "file": 3, "line": 48, "parent": 3}, {"file": 5, "parent": 10}, {"command": 2, "file": 5, "line": 161, "parent": 11}, {"command": 3, "file": 3, "line": 48, "parent": 3}, {"file": 6, "parent": 13}, {"command": 2, "file": 6, "line": 180, "parent": 14}, {"command": 3, "file": 3, "line": 48, "parent": 3}, {"file": 7, "parent": 16}, {"command": 2, "file": 7, "line": 157, "parent": 17}, {"command": 3, "file": 3, "line": 48, "parent": 3}, {"file": 8, "parent": 19}, {"command": 2, "file": 8, "line": 151, "parent": 20}, {"command": 3, "file": 3, "line": 48, "parent": 3}, {"file": 9, "parent": 22}, {"command": 2, "file": 9, "line": 151, "parent": 23}, {"command": 3, "file": 3, "line": 48, "parent": 3}, {"file": 10, "parent": 25}, {"command": 2, "file": 10, "line": 164, "parent": 26}, {"command": 3, "file": 3, "line": 48, "parent": 3}, {"file": 11, "parent": 28}, {"command": 2, "file": 11, "line": 151, "parent": 29}]}, "dependencies": [{"backtrace": 6, "id": "drill_msgs__rosidl_generator_type_description::@6890427a1f51a3e7e1df"}, {"backtrace": 9, "id": "drill_msgs__rosidl_generator_c::@6890427a1f51a3e7e1df"}, {"backtrace": 12, "id": "drill_msgs__rosidl_typesupport_fastrtps_c::@6890427a1f51a3e7e1df"}, {"backtrace": 15, "id": "drill_msgs__rosidl_typesupport_fastrtps_cpp::@6890427a1f51a3e7e1df"}, {"backtrace": 18, "id": "drill_msgs__rosidl_typesupport_introspection_c::@6890427a1f51a3e7e1df"}, {"backtrace": 21, "id": "drill_msgs__rosidl_typesupport_c::@6890427a1f51a3e7e1df"}, {"backtrace": 24, "id": "drill_msgs__rosidl_typesupport_introspection_cpp::@6890427a1f51a3e7e1df"}, {"backtrace": 27, "id": "drill_msgs__cpp::@6890427a1f51a3e7e1df"}, {"backtrace": 30, "id": "drill_msgs__rosidl_typesupport_cpp::@6890427a1f51a3e7e1df"}], "id": "drill_msgs::@6890427a1f51a3e7e1df", "name": "drill_msgs", "paths": {"build": ".", "source": "."}, "sourceGroups": [{"name": "", "sourceIndexes": [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54]}, {"name": "CMake Rules", "sourceIndexes": [55]}], "sources": [{"backtrace": 2, "path": "msg/Event.msg", "sourceGroupIndex": 0}, {"backtrace": 2, "path": "msg/Report.msg", "sourceGroupIndex": 0}, {"backtrace": 2, "path": "msg/Permission.msg", "sourceGroupIndex": 0}, {"backtrace": 2, "path": "msg/ParamNotification.msg", "sourceGroupIndex": 0}, {"backtrace": 2, "path": "msg/BoolStamped.msg", "sourceGroupIndex": 0}, {"backtrace": 2, "path": "msg/Vector2d.msg", "sourceGroupIndex": 0}, {"backtrace": 2, "path": "msg/OpenCloseAction.msg", "sourceGroupIndex": 0}, {"backtrace": 2, "path": "msg/IMU.msg", "sourceGroupIndex": 0}, {"backtrace": 2, "path": "msg/EngineState.msg", "sourceGroupIndex": 0}, {"backtrace": 2, "path": "msg/DrillStateRaw.msg", "sourceGroupIndex": 0}, {"backtrace": 2, "path": "msg/DrillState.msg", "sourceGroupIndex": 0}, {"backtrace": 2, "path": "msg/DepthInfo.msg", "sourceGroupIndex": 0}, {"backtrace": 2, "path": "msg/FloatStamped.msg", "sourceGroupIndex": 0}, {"backtrace": 2, "path": "msg/JacksStateRaw.msg", "sourceGroupIndex": 0}, {"backtrace": 2, "path": "msg/JacksSwitchState.msg", "sourceGroupIndex": 0}, {"backtrace": 2, "path": "msg/JacksSwitchStateRaw.msg", "sourceGroupIndex": 0}, {"backtrace": 2, "path": "msg/PinsStateRaw.msg", "sourceGroupIndex": 0}, {"backtrace": 2, "path": "msg/ArmStateRaw.msg", "sourceGroupIndex": 0}, {"backtrace": 2, "path": "msg/ArmState.msg", "sourceGroupIndex": 0}, {"backtrace": 2, "path": "msg/ForkStateRaw.msg", "sourceGroupIndex": 0}, {"backtrace": 2, "path": "msg/ForkState.msg", "sourceGroupIndex": 0}, {"backtrace": 2, "path": "msg/CarouselStateRaw.msg", "sourceGroupIndex": 0}, {"backtrace": 2, "path": "msg/WrenchStateRaw.msg", "sourceGroupIndex": 0}, {"backtrace": 2, "path": "msg/DustFlapsState.msg", "sourceGroupIndex": 0}, {"backtrace": 2, "path": "msg/StateMachineStatus.msg", "sourceGroupIndex": 0}, {"backtrace": 2, "path": "msg/StateCommand.msg", "sourceGroupIndex": 0}, {"backtrace": 2, "path": "msg/RmoHealth.msg", "sourceGroupIndex": 0}, {"backtrace": 2, "path": "msg/CarouselCtrl.msg", "sourceGroupIndex": 0}, {"backtrace": 2, "path": "msg/TowerCtrl.msg", "sourceGroupIndex": 0}, {"backtrace": 2, "path": "msg/WrenchCtrl.msg", "sourceGroupIndex": 0}, {"backtrace": 2, "path": "msg/TracksCtrl.msg", "sourceGroupIndex": 0}, {"backtrace": 2, "path": "msg/FloatCtrl.msg", "sourceGroupIndex": 0}, {"backtrace": 2, "path": "msg/DrillCtrl.msg", "sourceGroupIndex": 0}, {"backtrace": 2, "path": "msg/DrillActuatorCtrl.msg", "sourceGroupIndex": 0}, {"backtrace": 2, "path": "msg/AirCtrl.msg", "sourceGroupIndex": 0}, {"backtrace": 2, "path": "msg/JacksCtrl.msg", "sourceGroupIndex": 0}, {"backtrace": 2, "path": "msg/UpsStatus.msg", "sourceGroupIndex": 0}, {"backtrace": 2, "path": "msg/LampCtrl.msg", "sourceGroupIndex": 0}, {"backtrace": 2, "path": "msg/Level.msg", "sourceGroupIndex": 0}, {"backtrace": 2, "path": "msg/GNSS.msg", "sourceGroupIndex": 0}, {"backtrace": 2, "path": "msg/Position.msg", "sourceGroupIndex": 0}, {"backtrace": 2, "path": "msg/SpeedState.msg", "sourceGroupIndex": 0}, {"backtrace": 2, "path": "msg/TracksState.msg", "sourceGroupIndex": 0}, {"backtrace": 2, "path": "msg/TowerState.msg", "sourceGroupIndex": 0}, {"backtrace": 2, "path": "msg/ModeCtrl.msg", "sourceGroupIndex": 0}, {"backtrace": 2, "path": "msg/Point2d.msg", "sourceGroupIndex": 0}, {"backtrace": 2, "path": "msg/Path.msg", "sourceGroupIndex": 0}, {"backtrace": 2, "path": "msg/PathPoint.msg", "sourceGroupIndex": 0}, {"backtrace": 2, "path": "msg/MainAction.msg", "sourceGroupIndex": 0}, {"backtrace": 2, "path": "msg/DrillerAction.msg", "sourceGroupIndex": 0}, {"backtrace": 2, "path": "msg/RockType.msg", "sourceGroupIndex": 0}, {"backtrace": 2, "path": "msg/DriveAction.msg", "sourceGroupIndex": 0}, {"backtrace": 2, "path": "msg/DriveStatus.msg", "sourceGroupIndex": 0}, {"backtrace": 2, "path": "srv/GetCurrentDriveAction.srv", "sourceGroupIndex": 0}, {"backtrace": 2, "isGenerated": true, "path": "/Users/<USER>/Work/drill2/onboard/build/drill_msgs/CMakeFiles/drill_msgs", "sourceGroupIndex": 0}, {"backtrace": 0, "isGenerated": true, "path": "/Users/<USER>/Work/drill2/onboard/build/drill_msgs/CMakeFiles/drill_msgs.rule", "sourceGroupIndex": 1}], "type": "UTILITY"}