{"configurations": [{"directories": [{"build": ".", "childIndexes": [1], "hasInstallRule": true, "jsonFile": "directory-.-RelWithDebInfo-3d60edbf35eae3c5fab9.json", "minimumCMakeVersion": {"string": "3.20"}, "projectIndex": 0, "source": ".", "targetIndexes": [0, 1, 2, 3, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18]}, {"build": "drill_msgs__py", "jsonFile": "directory-drill_msgs__py-RelWithDebInfo-6fbd088d94f6cb96acf6.json", "minimumCMakeVersion": {"string": "3.20"}, "parentIndex": 0, "projectIndex": 0, "source": "/Users/<USER>/Work/drill2/onboard/build/drill_msgs/drill_msgs__py", "targetIndexes": [4]}], "name": "RelWithDebInfo", "projects": [{"directoryIndexes": [0, 1], "name": "drill_msgs", "targetIndexes": [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18]}], "targets": [{"directoryIndex": 0, "id": "ament_cmake_python_build_drill_msgs_egg::@6890427a1f51a3e7e1df", "jsonFile": "target-ament_cmake_python_build_drill_msgs_egg-RelWithDebInfo-8fc08d0d30c830b47936.json", "name": "ament_cmake_python_build_drill_msgs_egg", "projectIndex": 0}, {"directoryIndex": 0, "id": "ament_cmake_python_symlink_drill_msgs::@6890427a1f51a3e7e1df", "jsonFile": "target-ament_cmake_python_symlink_drill_msgs-RelWithDebInfo-66e7692a14c75468470e.json", "name": "ament_cmake_python_symlink_drill_msgs", "projectIndex": 0}, {"directoryIndex": 0, "id": "drill_msgs::@6890427a1f51a3e7e1df", "jsonFile": "target-drill_msgs-RelWithDebInfo-08aca8e850c73bfaffe6.json", "name": "drill_msgs", "projectIndex": 0}, {"directoryIndex": 0, "id": "drill_msgs__cpp::@6890427a1f51a3e7e1df", "jsonFile": "target-drill_msgs__cpp-RelWithDebInfo-d9d5df3642d579326c0c.json", "name": "drill_msgs__cpp", "projectIndex": 0}, {"directoryIndex": 1, "id": "drill_msgs__py::@aae8e37d1fcda10b9a09", "jsonFile": "target-drill_msgs__py-RelWithDebInfo-318e47aa66eb0e4f9dcf.json", "name": "drill_msgs__py", "projectIndex": 0}, {"directoryIndex": 0, "id": "drill_msgs__rosidl_generator_c::@6890427a1f51a3e7e1df", "jsonFile": "target-drill_msgs__rosidl_generator_c-RelWithDebInfo-e655695bed6187e8c763.json", "name": "drill_msgs__rosidl_generator_c", "projectIndex": 0}, {"directoryIndex": 0, "id": "drill_msgs__rosidl_generator_py::@6890427a1f51a3e7e1df", "jsonFile": "target-drill_msgs__rosidl_generator_py-RelWithDebInfo-025f8a8fd4c1740bd696.json", "name": "drill_msgs__rosidl_generator_py", "projectIndex": 0}, {"directoryIndex": 0, "id": "drill_msgs__rosidl_generator_type_description::@6890427a1f51a3e7e1df", "jsonFile": "target-drill_msgs__rosidl_generator_type_description-RelWithDebInfo-85c3c5f7c620dfa52d39.json", "name": "drill_msgs__rosidl_generator_type_description", "projectIndex": 0}, {"directoryIndex": 0, "id": "drill_msgs__rosidl_typesupport_c::@6890427a1f51a3e7e1df", "jsonFile": "target-drill_msgs__rosidl_typesupport_c-RelWithDebInfo-bb32dc3ba439f6320385.json", "name": "drill_msgs__rosidl_typesupport_c", "projectIndex": 0}, {"directoryIndex": 0, "id": "drill_msgs__rosidl_typesupport_cpp::@6890427a1f51a3e7e1df", "jsonFile": "target-drill_msgs__rosidl_typesupport_cpp-RelWithDebInfo-4762123f2667f8d3fd97.json", "name": "drill_msgs__rosidl_typesupport_cpp", "projectIndex": 0}, {"directoryIndex": 0, "id": "drill_msgs__rosidl_typesupport_fastrtps_c::@6890427a1f51a3e7e1df", "jsonFile": "target-drill_msgs__rosidl_typesupport_fastrtps_c-RelWithDebInfo-ecf074bc4c343d894b45.json", "name": "drill_msgs__rosidl_typesupport_fastrtps_c", "projectIndex": 0}, {"directoryIndex": 0, "id": "drill_msgs__rosidl_typesupport_fastrtps_cpp::@6890427a1f51a3e7e1df", "jsonFile": "target-drill_msgs__rosidl_typesupport_fastrtps_cpp-RelWithDebInfo-792b87eaf4ad41fb0d81.json", "name": "drill_msgs__rosidl_typesupport_fastrtps_cpp", "projectIndex": 0}, {"directoryIndex": 0, "id": "drill_msgs__rosidl_typesupport_introspection_c::@6890427a1f51a3e7e1df", "jsonFile": "target-drill_msgs__rosidl_typesupport_introspection_c-RelWithDebInfo-978eb3a0457b1390781d.json", "name": "drill_msgs__rosidl_typesupport_introspection_c", "projectIndex": 0}, {"directoryIndex": 0, "id": "drill_msgs__rosidl_typesupport_introspection_cpp::@6890427a1f51a3e7e1df", "jsonFile": "target-drill_msgs__rosidl_typesupport_introspection_cpp-RelWithDebInfo-11914d96b0edfe162ada.json", "name": "drill_msgs__rosidl_typesupport_introspection_cpp", "projectIndex": 0}, {"directoryIndex": 0, "id": "drill_msgs_s__rosidl_typesupport_c::@6890427a1f51a3e7e1df", "jsonFile": "target-drill_msgs_s__rosidl_typesupport_c-RelWithDebInfo-9da053e17770f1c49805.json", "name": "drill_msgs_s__rosidl_typesupport_c", "projectIndex": 0}, {"directoryIndex": 0, "id": "drill_msgs_s__rosidl_typesupport_fastrtps_c::@6890427a1f51a3e7e1df", "jsonFile": "target-drill_msgs_s__rosidl_typesupport_fastrtps_c-RelWithDebInfo-d085e725786f398e47f5.json", "name": "drill_msgs_s__rosidl_typesupport_fastrtps_c", "projectIndex": 0}, {"directoryIndex": 0, "id": "drill_msgs_s__rosidl_typesupport_introspection_c::@6890427a1f51a3e7e1df", "jsonFile": "target-drill_msgs_s__rosidl_typesupport_introspection_c-RelWithDebInfo-9973a49b4eb667c84602.json", "name": "drill_msgs_s__rosidl_typesupport_introspection_c", "projectIndex": 0}, {"directoryIndex": 0, "id": "drill_msgs_uninstall::@6890427a1f51a3e7e1df", "jsonFile": "target-drill_msgs_uninstall-RelWithDebInfo-b9a174ce1374e1dccadc.json", "name": "drill_msgs_uninstall", "projectIndex": 0}, {"directoryIndex": 0, "id": "uninstall::@6890427a1f51a3e7e1df", "jsonFile": "target-uninstall-RelWithDebInfo-b569052cc5edf2e54069.json", "name": "uninstall", "projectIndex": 0}]}], "kind": "codemodel", "paths": {"build": "/Users/<USER>/Work/drill2/onboard/build/drill_msgs", "source": "/Users/<USER>/Work/drill2/onboard/src/drill_msgs"}, "version": {"major": 2, "minor": 8}}