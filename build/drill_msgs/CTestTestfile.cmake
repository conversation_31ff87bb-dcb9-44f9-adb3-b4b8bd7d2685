# CMake generated Testfile for 
# Source directory: /Users/<USER>/Work/drill2/onboard/src/drill_msgs
# Build directory: /Users/<USER>/Work/drill2/onboard/build/drill_msgs
# 
# This file includes the relevant testing commands required for 
# testing this directory and lists subdirectories to be tested as well.
add_test(lint_cmake "/Users/<USER>/.ros2_venv/bin/python3" "-u" "/Users/<USER>/ros2_jazzy/install/ament_cmake_test/share/ament_cmake_test/cmake/run_test.py" "/Users/<USER>/Work/drill2/onboard/build/drill_msgs/test_results/drill_msgs/lint_cmake.xunit.xml" "--package-name" "drill_msgs" "--output-file" "/Users/<USER>/Work/drill2/onboard/build/drill_msgs/ament_lint_cmake/lint_cmake.txt" "--command" "/Users/<USER>/ros2_jazzy/install/ament_lint_cmake/bin/ament_lint_cmake" "--xunit-file" "/Users/<USER>/Work/drill2/onboard/build/drill_msgs/test_results/drill_msgs/lint_cmake.xunit.xml")
set_tests_properties(lint_cmake PROPERTIES  LABELS "lint_cmake;linter" TIMEOUT "60" WORKING_DIRECTORY "/Users/<USER>/Work/drill2/onboard/src/drill_msgs" _BACKTRACE_TRIPLES "/Users/<USER>/ros2_jazzy/install/ament_cmake_test/share/ament_cmake_test/cmake/ament_add_test.cmake;125;add_test;/Users/<USER>/ros2_jazzy/install/ament_cmake_lint_cmake/share/ament_cmake_lint_cmake/cmake/ament_lint_cmake.cmake;47;ament_add_test;/Users/<USER>/ros2_jazzy/install/ament_cmake_lint_cmake/share/ament_cmake_lint_cmake/cmake/ament_cmake_lint_cmake_lint_hook.cmake;21;ament_lint_cmake;/Users/<USER>/ros2_jazzy/install/ament_cmake_lint_cmake/share/ament_cmake_lint_cmake/cmake/ament_cmake_lint_cmake_lint_hook.cmake;0;;/Users/<USER>/ros2_jazzy/install/ament_cmake_core/share/ament_cmake_core/cmake/core/ament_execute_extensions.cmake;48;include;/Users/<USER>/ros2_jazzy/install/ament_lint_auto/share/ament_lint_auto/cmake/ament_lint_auto_package_hook.cmake;21;ament_execute_extensions;/Users/<USER>/ros2_jazzy/install/ament_lint_auto/share/ament_lint_auto/cmake/ament_lint_auto_package_hook.cmake;0;;/Users/<USER>/ros2_jazzy/install/ament_cmake_core/share/ament_cmake_core/cmake/core/ament_execute_extensions.cmake;48;include;/Users/<USER>/ros2_jazzy/install/ament_cmake_core/share/ament_cmake_core/cmake/core/ament_package.cmake;66;ament_execute_extensions;/Users/<USER>/Work/drill2/onboard/src/drill_msgs/CMakeLists.txt;87;ament_package;/Users/<USER>/Work/drill2/onboard/src/drill_msgs/CMakeLists.txt;0;")
add_test(xmllint "/Users/<USER>/.ros2_venv/bin/python3" "-u" "/Users/<USER>/ros2_jazzy/install/ament_cmake_test/share/ament_cmake_test/cmake/run_test.py" "/Users/<USER>/Work/drill2/onboard/build/drill_msgs/test_results/drill_msgs/xmllint.xunit.xml" "--package-name" "drill_msgs" "--output-file" "/Users/<USER>/Work/drill2/onboard/build/drill_msgs/ament_xmllint/xmllint.txt" "--command" "/Users/<USER>/ros2_jazzy/install/ament_xmllint/bin/ament_xmllint" "--xunit-file" "/Users/<USER>/Work/drill2/onboard/build/drill_msgs/test_results/drill_msgs/xmllint.xunit.xml")
set_tests_properties(xmllint PROPERTIES  LABELS "xmllint;linter" TIMEOUT "60" WORKING_DIRECTORY "/Users/<USER>/Work/drill2/onboard/src/drill_msgs" _BACKTRACE_TRIPLES "/Users/<USER>/ros2_jazzy/install/ament_cmake_test/share/ament_cmake_test/cmake/ament_add_test.cmake;125;add_test;/Users/<USER>/ros2_jazzy/install/ament_cmake_xmllint/share/ament_cmake_xmllint/cmake/ament_xmllint.cmake;50;ament_add_test;/Users/<USER>/ros2_jazzy/install/ament_cmake_xmllint/share/ament_cmake_xmllint/cmake/ament_cmake_xmllint_lint_hook.cmake;18;ament_xmllint;/Users/<USER>/ros2_jazzy/install/ament_cmake_xmllint/share/ament_cmake_xmllint/cmake/ament_cmake_xmllint_lint_hook.cmake;0;;/Users/<USER>/ros2_jazzy/install/ament_cmake_core/share/ament_cmake_core/cmake/core/ament_execute_extensions.cmake;48;include;/Users/<USER>/ros2_jazzy/install/ament_lint_auto/share/ament_lint_auto/cmake/ament_lint_auto_package_hook.cmake;21;ament_execute_extensions;/Users/<USER>/ros2_jazzy/install/ament_lint_auto/share/ament_lint_auto/cmake/ament_lint_auto_package_hook.cmake;0;;/Users/<USER>/ros2_jazzy/install/ament_cmake_core/share/ament_cmake_core/cmake/core/ament_execute_extensions.cmake;48;include;/Users/<USER>/ros2_jazzy/install/ament_cmake_core/share/ament_cmake_core/cmake/core/ament_package.cmake;66;ament_execute_extensions;/Users/<USER>/Work/drill2/onboard/src/drill_msgs/CMakeLists.txt;87;ament_package;/Users/<USER>/Work/drill2/onboard/src/drill_msgs/CMakeLists.txt;0;")
subdirs("drill_msgs__py")
