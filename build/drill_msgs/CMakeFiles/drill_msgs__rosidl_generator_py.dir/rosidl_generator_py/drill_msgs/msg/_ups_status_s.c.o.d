CMakeFiles/drill_msgs__rosidl_generator_py.dir/rosidl_generator_py/drill_msgs/msg/_ups_status_s.c.o: \
  /Users/<USER>/Work/drill2/onboard/build/drill_msgs/rosidl_generator_py/drill_msgs/msg/_ups_status_s.c \
  /opt/homebrew/opt/python@3.11/Frameworks/Python.framework/Versions/3.11/include/python3.11/Python.h \
  /opt/homebrew/opt/python@3.11/Frameworks/Python.framework/Versions/3.11/include/python3.11/patchlevel.h \
  /opt/homebrew/opt/python@3.11/Frameworks/Python.framework/Versions/3.11/include/python3.11/pyconfig.h \
  /opt/homebrew/opt/python@3.11/Frameworks/Python.framework/Versions/3.11/include/python3.11/pymacconfig.h \
  /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX.sdk/usr/include/stdlib.h \
  /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX.sdk/usr/include/_stdlib.h \
  /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX.sdk/usr/include/Availability.h \
  /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX.sdk/usr/include/AvailabilityVersions.h \
  /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX.sdk/usr/include/AvailabilityInternal.h \
  /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX.sdk/usr/include/AvailabilityInternalLegacy.h \
  /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX.sdk/usr/include/sys/cdefs.h \
  /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX.sdk/usr/include/sys/_symbol_aliasing.h \
  /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX.sdk/usr/include/sys/_posix_availability.h \
  /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/lib/clang/17/include/ptrcheck.h \
  /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX.sdk/usr/include/_bounds.h \
  /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX.sdk/usr/include/_types.h \
  /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX.sdk/usr/include/sys/_types.h \
  /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX.sdk/usr/include/machine/_types.h \
  /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX.sdk/usr/include/arm/_types.h \
  /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX.sdk/usr/include/sys/_pthread/_pthread_types.h \
  /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX.sdk/usr/include/sys/wait.h \
  /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX.sdk/usr/include/sys/_types/_pid_t.h \
  /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX.sdk/usr/include/sys/_types/_id_t.h \
  /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX.sdk/usr/include/sys/signal.h \
  /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX.sdk/usr/include/sys/appleapiopts.h \
  /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX.sdk/usr/include/machine/signal.h \
  /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX.sdk/usr/include/arm/signal.h \
  /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX.sdk/usr/include/machine/_mcontext.h \
  /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX.sdk/usr/include/arm/_mcontext.h \
  /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX.sdk/usr/include/mach/machine/_structs.h \
  /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX.sdk/usr/include/mach/arm/_structs.h \
  /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX.sdk/usr/include/machine/types.h \
  /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX.sdk/usr/include/arm/types.h \
  /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX.sdk/usr/include/sys/_types/_int8_t.h \
  /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX.sdk/usr/include/sys/_types/_int16_t.h \
  /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX.sdk/usr/include/sys/_types/_int32_t.h \
  /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX.sdk/usr/include/sys/_types/_int64_t.h \
  /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX.sdk/usr/include/sys/_types/_u_int8_t.h \
  /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX.sdk/usr/include/sys/_types/_u_int16_t.h \
  /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX.sdk/usr/include/sys/_types/_u_int32_t.h \
  /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX.sdk/usr/include/sys/_types/_u_int64_t.h \
  /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX.sdk/usr/include/sys/_types/_intptr_t.h \
  /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX.sdk/usr/include/sys/_types/_uintptr_t.h \
  /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX.sdk/usr/include/sys/_pthread/_pthread_attr_t.h \
  /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX.sdk/usr/include/sys/_types/_sigaltstack.h \
  /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX.sdk/usr/include/sys/_types/_ucontext.h \
  /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX.sdk/usr/include/sys/_types/_sigset_t.h \
  /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX.sdk/usr/include/sys/_types/_size_t.h \
  /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX.sdk/usr/include/sys/_types/_uid_t.h \
  /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX.sdk/usr/include/sys/resource.h \
  /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/lib/clang/17/include/stdint.h \
  /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX.sdk/usr/include/stdint.h \
  /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX.sdk/usr/include/_types/_uint8_t.h \
  /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX.sdk/usr/include/_types/_uint16_t.h \
  /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX.sdk/usr/include/_types/_uint32_t.h \
  /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX.sdk/usr/include/_types/_uint64_t.h \
  /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX.sdk/usr/include/_types/_intmax_t.h \
  /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX.sdk/usr/include/_types/_uintmax_t.h \
  /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX.sdk/usr/include/sys/_types/_timeval.h \
  /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX.sdk/usr/include/machine/endian.h \
  /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX.sdk/usr/include/arm/endian.h \
  /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX.sdk/usr/include/sys/_endian.h \
  /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX.sdk/usr/include/machine/_endian.h \
  /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX.sdk/usr/include/arm/_endian.h \
  /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX.sdk/usr/include/sys/__endian.h \
  /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX.sdk/usr/include/libkern/_OSByteOrder.h \
  /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX.sdk/usr/include/libkern/arm/_OSByteOrder.h \
  /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX.sdk/usr/include/alloca.h \
  /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX.sdk/usr/include/sys/_types/_ct_rune_t.h \
  /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX.sdk/usr/include/sys/_types/_rune_t.h \
  /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX.sdk/usr/include/sys/_types/_wchar_t.h \
  /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX.sdk/usr/include/sys/_types/_null.h \
  /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX.sdk/usr/include/malloc/_malloc.h \
  /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX.sdk/usr/include/malloc/_malloc_type.h \
  /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX.sdk/usr/include/malloc/_ptrcheck.h \
  /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX.sdk/usr/include/_abort.h \
  /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX.sdk/usr/include/sys/_types/_dev_t.h \
  /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX.sdk/usr/include/sys/_types/_mode_t.h \
  /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX.sdk/usr/include/stdio.h \
  /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX.sdk/usr/include/_stdio.h \
  /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX.sdk/usr/include/sys/_types/_va_list.h \
  /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX.sdk/usr/include/sys/stdio.h \
  /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX.sdk/usr/include/_printf.h \
  /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX.sdk/usr/include/sys/_types/_seek_set.h \
  /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX.sdk/usr/include/_ctermid.h \
  /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX.sdk/usr/include/sys/_types/_off_t.h \
  /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX.sdk/usr/include/sys/_types/_ssize_t.h \
  /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX.sdk/usr/include/secure/_stdio.h \
  /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX.sdk/usr/include/secure/_common.h \
  /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX.sdk/usr/include/errno.h \
  /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX.sdk/usr/include/sys/errno.h \
  /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX.sdk/usr/include/sys/_types/_errno_t.h \
  /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX.sdk/usr/include/string.h \
  /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX.sdk/usr/include/_string.h \
  /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX.sdk/usr/include/sys/_types/_rsize_t.h \
  /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX.sdk/usr/include/_strings.h \
  /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX.sdk/usr/include/secure/_strings.h \
  /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX.sdk/usr/include/secure/_string.h \
  /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX.sdk/usr/include/unistd.h \
  /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX.sdk/usr/include/sys/unistd.h \
  /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX.sdk/usr/include/sys/_types/_posix_vdisable.h \
  /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX.sdk/usr/include/sys/_types/_gid_t.h \
  /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX.sdk/usr/include/sys/_types/_useconds_t.h \
  /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX.sdk/usr/include/sys/select.h \
  /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX.sdk/usr/include/sys/_types/_fd_def.h \
  /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX.sdk/usr/include/sys/_types/_timespec.h \
  /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX.sdk/usr/include/sys/_types/_time_t.h \
  /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX.sdk/usr/include/sys/_types/_suseconds_t.h \
  /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX.sdk/usr/include/sys/_types/_fd_setsize.h \
  /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX.sdk/usr/include/sys/_types/_fd_set.h \
  /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX.sdk/usr/include/sys/_types/_fd_clr.h \
  /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX.sdk/usr/include/sys/_types/_fd_isset.h \
  /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX.sdk/usr/include/sys/_types/_fd_zero.h \
  /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX.sdk/usr/include/sys/_types/_fd_copy.h \
  /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX.sdk/usr/include/sys/_select.h \
  /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX.sdk/usr/include/sys/_types/_uuid_t.h \
  /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX.sdk/usr/include/gethostuuid.h \
  /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX.sdk/usr/include/assert.h \
  /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX.sdk/usr/include/_static_assert.h \
  /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX.sdk/usr/include/wchar.h \
  /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX.sdk/usr/include/_wchar.h \
  /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX.sdk/usr/include/sys/_types/_mbstate_t.h \
  /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/lib/clang/17/include/stdarg.h \
  /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/lib/clang/17/include/__stdarg_header_macro.h \
  /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/lib/clang/17/include/__stdarg___gnuc_va_list.h \
  /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/lib/clang/17/include/__stdarg_va_list.h \
  /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/lib/clang/17/include/__stdarg_va_arg.h \
  /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/lib/clang/17/include/__stdarg___va_copy.h \
  /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/lib/clang/17/include/__stdarg_va_copy.h \
  /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX.sdk/usr/include/time.h \
  /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX.sdk/usr/include/_time.h \
  /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX.sdk/usr/include/sys/_types/_clock_t.h \
  /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX.sdk/usr/include/__wctype.h \
  /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX.sdk/usr/include/___wctype.h \
  /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX.sdk/usr/include/sys/_types/_wint_t.h \
  /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX.sdk/usr/include/_types/_wctype_t.h \
  /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX.sdk/usr/include/ctype.h \
  /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX.sdk/usr/include/_ctype.h \
  /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX.sdk/usr/include/runetype.h \
  /opt/homebrew/opt/python@3.11/Frameworks/Python.framework/Versions/3.11/include/python3.11/pyport.h \
  /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/lib/clang/17/include/inttypes.h \
  /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX.sdk/usr/include/inttypes.h \
  /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX.sdk/usr/include/_inttypes.h \
  /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/lib/clang/17/include/limits.h \
  /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX.sdk/usr/include/limits.h \
  /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX.sdk/usr/include/machine/limits.h \
  /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX.sdk/usr/include/arm/limits.h \
  /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX.sdk/usr/include/arm/_limits.h \
  /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX.sdk/usr/include/sys/syslimits.h \
  /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX.sdk/usr/include/math.h \
  /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX.sdk/usr/include/sys/time.h \
  /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX.sdk/usr/include/sys/_types/_timeval64.h \
  /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX.sdk/usr/include/sys/stat.h \
  /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX.sdk/usr/include/sys/_types/_blkcnt_t.h \
  /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX.sdk/usr/include/sys/_types/_blksize_t.h \
  /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX.sdk/usr/include/sys/_types/_ino_t.h \
  /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX.sdk/usr/include/sys/_types/_ino64_t.h \
  /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX.sdk/usr/include/sys/_types/_nlink_t.h \
  /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX.sdk/usr/include/sys/_types/_s_ifmt.h \
  /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX.sdk/usr/include/sys/_types/_filesec_t.h \
  /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX.sdk/usr/include/wctype.h \
  /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX.sdk/usr/include/_wctype.h \
  /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX.sdk/usr/include/_types/_wctrans_t.h \
  /opt/homebrew/opt/python@3.11/Frameworks/Python.framework/Versions/3.11/include/python3.11/exports.h \
  /opt/homebrew/opt/python@3.11/Frameworks/Python.framework/Versions/3.11/include/python3.11/pymacro.h \
  /opt/homebrew/opt/python@3.11/Frameworks/Python.framework/Versions/3.11/include/python3.11/pymath.h \
  /opt/homebrew/opt/python@3.11/Frameworks/Python.framework/Versions/3.11/include/python3.11/pymem.h \
  /opt/homebrew/opt/python@3.11/Frameworks/Python.framework/Versions/3.11/include/python3.11/cpython/pymem.h \
  /opt/homebrew/opt/python@3.11/Frameworks/Python.framework/Versions/3.11/include/python3.11/pytypedefs.h \
  /opt/homebrew/opt/python@3.11/Frameworks/Python.framework/Versions/3.11/include/python3.11/pybuffer.h \
  /opt/homebrew/opt/python@3.11/Frameworks/Python.framework/Versions/3.11/include/python3.11/object.h \
  /opt/homebrew/opt/python@3.11/Frameworks/Python.framework/Versions/3.11/include/python3.11/cpython/object.h \
  /opt/homebrew/opt/python@3.11/Frameworks/Python.framework/Versions/3.11/include/python3.11/objimpl.h \
  /opt/homebrew/opt/python@3.11/Frameworks/Python.framework/Versions/3.11/include/python3.11/cpython/objimpl.h \
  /opt/homebrew/opt/python@3.11/Frameworks/Python.framework/Versions/3.11/include/python3.11/typeslots.h \
  /opt/homebrew/opt/python@3.11/Frameworks/Python.framework/Versions/3.11/include/python3.11/pyhash.h \
  /opt/homebrew/opt/python@3.11/Frameworks/Python.framework/Versions/3.11/include/python3.11/cpython/pydebug.h \
  /opt/homebrew/opt/python@3.11/Frameworks/Python.framework/Versions/3.11/include/python3.11/bytearrayobject.h \
  /opt/homebrew/opt/python@3.11/Frameworks/Python.framework/Versions/3.11/include/python3.11/cpython/bytearrayobject.h \
  /opt/homebrew/opt/python@3.11/Frameworks/Python.framework/Versions/3.11/include/python3.11/bytesobject.h \
  /opt/homebrew/opt/python@3.11/Frameworks/Python.framework/Versions/3.11/include/python3.11/cpython/bytesobject.h \
  /opt/homebrew/opt/python@3.11/Frameworks/Python.framework/Versions/3.11/include/python3.11/unicodeobject.h \
  /opt/homebrew/opt/python@3.11/Frameworks/Python.framework/Versions/3.11/include/python3.11/cpython/unicodeobject.h \
  /opt/homebrew/opt/python@3.11/Frameworks/Python.framework/Versions/3.11/include/python3.11/cpython/code.h \
  /opt/homebrew/opt/python@3.11/Frameworks/Python.framework/Versions/3.11/include/python3.11/cpython/initconfig.h \
  /opt/homebrew/opt/python@3.11/Frameworks/Python.framework/Versions/3.11/include/python3.11/pystate.h \
  /opt/homebrew/opt/python@3.11/Frameworks/Python.framework/Versions/3.11/include/python3.11/cpython/pystate.h \
  /opt/homebrew/opt/python@3.11/Frameworks/Python.framework/Versions/3.11/include/python3.11/pyerrors.h \
  /opt/homebrew/opt/python@3.11/Frameworks/Python.framework/Versions/3.11/include/python3.11/cpython/pyerrors.h \
  /opt/homebrew/opt/python@3.11/Frameworks/Python.framework/Versions/3.11/include/python3.11/longobject.h \
  /opt/homebrew/opt/python@3.11/Frameworks/Python.framework/Versions/3.11/include/python3.11/cpython/longobject.h \
  /opt/homebrew/opt/python@3.11/Frameworks/Python.framework/Versions/3.11/include/python3.11/cpython/longintrepr.h \
  /opt/homebrew/opt/python@3.11/Frameworks/Python.framework/Versions/3.11/include/python3.11/boolobject.h \
  /opt/homebrew/opt/python@3.11/Frameworks/Python.framework/Versions/3.11/include/python3.11/floatobject.h \
  /opt/homebrew/opt/python@3.11/Frameworks/Python.framework/Versions/3.11/include/python3.11/cpython/floatobject.h \
  /opt/homebrew/opt/python@3.11/Frameworks/Python.framework/Versions/3.11/include/python3.11/complexobject.h \
  /opt/homebrew/opt/python@3.11/Frameworks/Python.framework/Versions/3.11/include/python3.11/cpython/complexobject.h \
  /opt/homebrew/opt/python@3.11/Frameworks/Python.framework/Versions/3.11/include/python3.11/rangeobject.h \
  /opt/homebrew/opt/python@3.11/Frameworks/Python.framework/Versions/3.11/include/python3.11/memoryobject.h \
  /opt/homebrew/opt/python@3.11/Frameworks/Python.framework/Versions/3.11/include/python3.11/tupleobject.h \
  /opt/homebrew/opt/python@3.11/Frameworks/Python.framework/Versions/3.11/include/python3.11/cpython/tupleobject.h \
  /opt/homebrew/opt/python@3.11/Frameworks/Python.framework/Versions/3.11/include/python3.11/listobject.h \
  /opt/homebrew/opt/python@3.11/Frameworks/Python.framework/Versions/3.11/include/python3.11/cpython/listobject.h \
  /opt/homebrew/opt/python@3.11/Frameworks/Python.framework/Versions/3.11/include/python3.11/dictobject.h \
  /opt/homebrew/opt/python@3.11/Frameworks/Python.framework/Versions/3.11/include/python3.11/cpython/dictobject.h \
  /opt/homebrew/opt/python@3.11/Frameworks/Python.framework/Versions/3.11/include/python3.11/cpython/odictobject.h \
  /opt/homebrew/opt/python@3.11/Frameworks/Python.framework/Versions/3.11/include/python3.11/enumobject.h \
  /opt/homebrew/opt/python@3.11/Frameworks/Python.framework/Versions/3.11/include/python3.11/setobject.h \
  /opt/homebrew/opt/python@3.11/Frameworks/Python.framework/Versions/3.11/include/python3.11/cpython/setobject.h \
  /opt/homebrew/opt/python@3.11/Frameworks/Python.framework/Versions/3.11/include/python3.11/methodobject.h \
  /opt/homebrew/opt/python@3.11/Frameworks/Python.framework/Versions/3.11/include/python3.11/cpython/methodobject.h \
  /opt/homebrew/opt/python@3.11/Frameworks/Python.framework/Versions/3.11/include/python3.11/moduleobject.h \
  /opt/homebrew/opt/python@3.11/Frameworks/Python.framework/Versions/3.11/include/python3.11/cpython/funcobject.h \
  /opt/homebrew/opt/python@3.11/Frameworks/Python.framework/Versions/3.11/include/python3.11/cpython/classobject.h \
  /opt/homebrew/opt/python@3.11/Frameworks/Python.framework/Versions/3.11/include/python3.11/fileobject.h \
  /opt/homebrew/opt/python@3.11/Frameworks/Python.framework/Versions/3.11/include/python3.11/cpython/fileobject.h \
  /opt/homebrew/opt/python@3.11/Frameworks/Python.framework/Versions/3.11/include/python3.11/pycapsule.h \
  /opt/homebrew/opt/python@3.11/Frameworks/Python.framework/Versions/3.11/include/python3.11/pyframe.h \
  /opt/homebrew/opt/python@3.11/Frameworks/Python.framework/Versions/3.11/include/python3.11/cpython/pyframe.h \
  /opt/homebrew/opt/python@3.11/Frameworks/Python.framework/Versions/3.11/include/python3.11/traceback.h \
  /opt/homebrew/opt/python@3.11/Frameworks/Python.framework/Versions/3.11/include/python3.11/cpython/traceback.h \
  /opt/homebrew/opt/python@3.11/Frameworks/Python.framework/Versions/3.11/include/python3.11/sliceobject.h \
  /opt/homebrew/opt/python@3.11/Frameworks/Python.framework/Versions/3.11/include/python3.11/cpython/cellobject.h \
  /opt/homebrew/opt/python@3.11/Frameworks/Python.framework/Versions/3.11/include/python3.11/iterobject.h \
  /opt/homebrew/opt/python@3.11/Frameworks/Python.framework/Versions/3.11/include/python3.11/cpython/genobject.h \
  /opt/homebrew/opt/python@3.11/Frameworks/Python.framework/Versions/3.11/include/python3.11/descrobject.h \
  /opt/homebrew/opt/python@3.11/Frameworks/Python.framework/Versions/3.11/include/python3.11/cpython/descrobject.h \
  /opt/homebrew/opt/python@3.11/Frameworks/Python.framework/Versions/3.11/include/python3.11/genericaliasobject.h \
  /opt/homebrew/opt/python@3.11/Frameworks/Python.framework/Versions/3.11/include/python3.11/warnings.h \
  /opt/homebrew/opt/python@3.11/Frameworks/Python.framework/Versions/3.11/include/python3.11/cpython/warnings.h \
  /opt/homebrew/opt/python@3.11/Frameworks/Python.framework/Versions/3.11/include/python3.11/weakrefobject.h \
  /opt/homebrew/opt/python@3.11/Frameworks/Python.framework/Versions/3.11/include/python3.11/cpython/weakrefobject.h \
  /opt/homebrew/opt/python@3.11/Frameworks/Python.framework/Versions/3.11/include/python3.11/structseq.h \
  /opt/homebrew/opt/python@3.11/Frameworks/Python.framework/Versions/3.11/include/python3.11/cpython/picklebufobject.h \
  /opt/homebrew/opt/python@3.11/Frameworks/Python.framework/Versions/3.11/include/python3.11/cpython/pytime.h \
  /opt/homebrew/opt/python@3.11/Frameworks/Python.framework/Versions/3.11/include/python3.11/codecs.h \
  /opt/homebrew/opt/python@3.11/Frameworks/Python.framework/Versions/3.11/include/python3.11/pythread.h \
  /opt/homebrew/opt/python@3.11/Frameworks/Python.framework/Versions/3.11/include/python3.11/cpython/pythread.h \
  /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX.sdk/usr/include/pthread.h \
  /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX.sdk/usr/include/pthread/sched.h \
  /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX.sdk/usr/include/pthread/pthread_impl.h \
  /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX.sdk/usr/include/sys/_pthread/_pthread_cond_t.h \
  /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX.sdk/usr/include/sys/_pthread/_pthread_condattr_t.h \
  /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX.sdk/usr/include/sys/_pthread/_pthread_key_t.h \
  /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX.sdk/usr/include/sys/_pthread/_pthread_mutex_t.h \
  /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX.sdk/usr/include/sys/_pthread/_pthread_mutexattr_t.h \
  /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX.sdk/usr/include/sys/_pthread/_pthread_once_t.h \
  /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX.sdk/usr/include/sys/_pthread/_pthread_rwlock_t.h \
  /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX.sdk/usr/include/sys/_pthread/_pthread_rwlockattr_t.h \
  /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX.sdk/usr/include/sys/_pthread/_pthread_t.h \
  /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX.sdk/usr/include/pthread/qos.h \
  /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX.sdk/usr/include/sys/qos.h \
  /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX.sdk/usr/include/sys/_types/_mach_port_t.h \
  /opt/homebrew/opt/python@3.11/Frameworks/Python.framework/Versions/3.11/include/python3.11/cpython/context.h \
  /opt/homebrew/opt/python@3.11/Frameworks/Python.framework/Versions/3.11/include/python3.11/modsupport.h \
  /opt/homebrew/opt/python@3.11/Frameworks/Python.framework/Versions/3.11/include/python3.11/cpython/modsupport.h \
  /opt/homebrew/opt/python@3.11/Frameworks/Python.framework/Versions/3.11/include/python3.11/compile.h \
  /opt/homebrew/opt/python@3.11/Frameworks/Python.framework/Versions/3.11/include/python3.11/cpython/compile.h \
  /opt/homebrew/opt/python@3.11/Frameworks/Python.framework/Versions/3.11/include/python3.11/pythonrun.h \
  /opt/homebrew/opt/python@3.11/Frameworks/Python.framework/Versions/3.11/include/python3.11/cpython/pythonrun.h \
  /opt/homebrew/opt/python@3.11/Frameworks/Python.framework/Versions/3.11/include/python3.11/pylifecycle.h \
  /opt/homebrew/opt/python@3.11/Frameworks/Python.framework/Versions/3.11/include/python3.11/cpython/pylifecycle.h \
  /opt/homebrew/opt/python@3.11/Frameworks/Python.framework/Versions/3.11/include/python3.11/ceval.h \
  /opt/homebrew/opt/python@3.11/Frameworks/Python.framework/Versions/3.11/include/python3.11/cpython/ceval.h \
  /opt/homebrew/opt/python@3.11/Frameworks/Python.framework/Versions/3.11/include/python3.11/sysmodule.h \
  /opt/homebrew/opt/python@3.11/Frameworks/Python.framework/Versions/3.11/include/python3.11/cpython/sysmodule.h \
  /opt/homebrew/opt/python@3.11/Frameworks/Python.framework/Versions/3.11/include/python3.11/osmodule.h \
  /opt/homebrew/opt/python@3.11/Frameworks/Python.framework/Versions/3.11/include/python3.11/intrcheck.h \
  /opt/homebrew/opt/python@3.11/Frameworks/Python.framework/Versions/3.11/include/python3.11/import.h \
  /opt/homebrew/opt/python@3.11/Frameworks/Python.framework/Versions/3.11/include/python3.11/cpython/import.h \
  /opt/homebrew/opt/python@3.11/Frameworks/Python.framework/Versions/3.11/include/python3.11/abstract.h \
  /opt/homebrew/opt/python@3.11/Frameworks/Python.framework/Versions/3.11/include/python3.11/cpython/abstract.h \
  /opt/homebrew/opt/python@3.11/Frameworks/Python.framework/Versions/3.11/include/python3.11/bltinmodule.h \
  /opt/homebrew/opt/python@3.11/Frameworks/Python.framework/Versions/3.11/include/python3.11/cpython/pyctype.h \
  /opt/homebrew/opt/python@3.11/Frameworks/Python.framework/Versions/3.11/include/python3.11/pystrtod.h \
  /opt/homebrew/opt/python@3.11/Frameworks/Python.framework/Versions/3.11/include/python3.11/pystrcmp.h \
  /opt/homebrew/opt/python@3.11/Frameworks/Python.framework/Versions/3.11/include/python3.11/fileutils.h \
  /opt/homebrew/opt/python@3.11/Frameworks/Python.framework/Versions/3.11/include/python3.11/cpython/fileutils.h \
  /opt/homebrew/opt/python@3.11/Frameworks/Python.framework/Versions/3.11/include/python3.11/cpython/pyfpe.h \
  /opt/homebrew/opt/python@3.11/Frameworks/Python.framework/Versions/3.11/include/python3.11/tracemalloc.h \
  /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/lib/clang/17/include/stdbool.h \
  /Users/<USER>/.ros2_venv/lib/python3.11/site-packages/numpy/_core/include/numpy/ndarrayobject.h \
  /Users/<USER>/.ros2_venv/lib/python3.11/site-packages/numpy/_core/include/numpy/ndarraytypes.h \
  /Users/<USER>/.ros2_venv/lib/python3.11/site-packages/numpy/_core/include/numpy/npy_common.h \
  /Users/<USER>/.ros2_venv/lib/python3.11/site-packages/numpy/_core/include/numpy/numpyconfig.h \
  /Users/<USER>/.ros2_venv/lib/python3.11/site-packages/numpy/_core/include/numpy/_numpyconfig.h \
  /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX.sdk/usr/include/sys/types.h \
  /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX.sdk/usr/include/sys/_types/_u_char.h \
  /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX.sdk/usr/include/sys/_types/_u_short.h \
  /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX.sdk/usr/include/sys/_types/_u_int.h \
  /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX.sdk/usr/include/sys/_types/_caddr_t.h \
  /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX.sdk/usr/include/sys/_types/_in_addr_t.h \
  /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX.sdk/usr/include/sys/_types/_in_port_t.h \
  /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX.sdk/usr/include/sys/_types/_key_t.h \
  /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX.sdk/usr/include/sys/_types/_fsblkcnt_t.h \
  /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX.sdk/usr/include/sys/_types/_fsfilcnt_t.h \
  /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX.sdk/usr/include/complex.h \
  /Users/<USER>/.ros2_venv/lib/python3.11/site-packages/numpy/_core/include/numpy/npy_endian.h \
  /Users/<USER>/.ros2_venv/lib/python3.11/site-packages/numpy/_core/include/numpy/npy_cpu.h \
  /Users/<USER>/.ros2_venv/lib/python3.11/site-packages/numpy/_core/include/numpy/utils.h \
  /Users/<USER>/.ros2_venv/lib/python3.11/site-packages/numpy/_core/include/numpy/_neighborhood_iterator_imp.h \
  /Users/<USER>/.ros2_venv/lib/python3.11/site-packages/numpy/_core/include/numpy/dtype_api.h \
  /Users/<USER>/.ros2_venv/lib/python3.11/site-packages/numpy/_core/include/numpy/__multiarray_api.h \
  /Users/<USER>/.ros2_venv/lib/python3.11/site-packages/numpy/_core/include/numpy/_public_dtype_api_table.h \
  /Users/<USER>/.ros2_venv/lib/python3.11/site-packages/numpy/_core/include/numpy/npy_2_compat.h \
  /Users/<USER>/ros2_jazzy/install/rosidl_runtime_c/include/rosidl_runtime_c/rosidl_runtime_c/visibility_control.h \
  /Users/<USER>/Work/drill2/onboard/build/drill_msgs/rosidl_generator_c/drill_msgs/msg/detail/ups_status__struct.h \
  /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/lib/clang/17/include/stddef.h \
  /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX.sdk/usr/include/stddef.h \
  /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/lib/clang/17/include/__stddef_header_macro.h \
  /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/lib/clang/17/include/__stddef_ptrdiff_t.h \
  /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/lib/clang/17/include/__stddef_size_t.h \
  /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/lib/clang/17/include/__stddef_rsize_t.h \
  /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/lib/clang/17/include/__stddef_wchar_t.h \
  /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/lib/clang/17/include/__stddef_null.h \
  /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/lib/clang/17/include/__stddef_max_align_t.h \
  /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/lib/clang/17/include/__stddef_offsetof.h \
  /Users/<USER>/ros2_jazzy/install/std_msgs/include/std_msgs/std_msgs/msg/detail/header__struct.h \
  /Users/<USER>/ros2_jazzy/install/builtin_interfaces/include/builtin_interfaces/builtin_interfaces/msg/detail/time__struct.h \
  /Users/<USER>/ros2_jazzy/install/rosidl_runtime_c/include/rosidl_runtime_c/rosidl_runtime_c/string.h \
  /Users/<USER>/ros2_jazzy/install/rosidl_runtime_c/include/rosidl_runtime_c/rosidl_runtime_c/primitives_sequence.h \
  /Users/<USER>/Work/drill2/onboard/build/drill_msgs/rosidl_generator_c/drill_msgs/msg/detail/ups_status__functions.h \
  /Users/<USER>/ros2_jazzy/install/rosidl_runtime_c/include/rosidl_runtime_c/rosidl_runtime_c/action_type_support_struct.h \
  /Users/<USER>/ros2_jazzy/install/rosidl_runtime_c/include/rosidl_runtime_c/rosidl_runtime_c/message_type_support_struct.h \
  /Users/<USER>/ros2_jazzy/install/rosidl_runtime_c/include/rosidl_runtime_c/rosidl_runtime_c/type_description/type_description__struct.h \
  /Users/<USER>/ros2_jazzy/install/rosidl_runtime_c/include/rosidl_runtime_c/rosidl_runtime_c/type_description/individual_type_description__struct.h \
  /Users/<USER>/ros2_jazzy/install/rosidl_runtime_c/include/rosidl_runtime_c/rosidl_runtime_c/type_description/field__struct.h \
  /Users/<USER>/ros2_jazzy/install/rosidl_runtime_c/include/rosidl_runtime_c/rosidl_runtime_c/type_description/field_type__struct.h \
  /Users/<USER>/ros2_jazzy/install/rosidl_runtime_c/include/rosidl_runtime_c/rosidl_runtime_c/type_description/type_source__struct.h \
  /Users/<USER>/ros2_jazzy/install/rosidl_runtime_c/include/rosidl_runtime_c/rosidl_runtime_c/type_hash.h \
  /Users/<USER>/ros2_jazzy/install/rcutils/include/rcutils/rcutils/allocator.h \
  /Users/<USER>/ros2_jazzy/install/rcutils/include/rcutils/rcutils/macros.h \
  /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX.sdk/usr/include/TargetConditionals.h \
  /Users/<USER>/ros2_jazzy/install/rcutils/include/rcutils/rcutils/types/rcutils_ret.h \
  /Users/<USER>/ros2_jazzy/install/rcutils/include/rcutils/rcutils/visibility_control.h \
  /Users/<USER>/ros2_jazzy/install/rcutils/include/rcutils/rcutils/visibility_control_macros.h \
  /Users/<USER>/ros2_jazzy/install/rcutils/include/rcutils/rcutils/sha256.h \
  /Users/<USER>/ros2_jazzy/install/rosidl_typesupport_interface/include/rosidl_typesupport_interface/rosidl_typesupport_interface/macros.h \
  /Users/<USER>/ros2_jazzy/install/rosidl_runtime_c/include/rosidl_runtime_c/rosidl_runtime_c/service_type_support_struct.h \
  /Users/<USER>/Work/drill2/onboard/build/drill_msgs/rosidl_generator_c/drill_msgs/msg/rosidl_generator_c__visibility_control.h
