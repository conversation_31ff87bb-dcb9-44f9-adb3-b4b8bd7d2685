// generated from rosidl_typesupport_fastrtps_c/resource/idl__rosidl_typesupport_fastrtps_c.h.em
// with input from drill_msgs:msg/RockType.idl
// generated code does not contain a copyright notice
#ifndef DRILL_MSGS__MSG__DETAIL__ROCK_TYPE__ROSIDL_TYPESUPPORT_FASTRTPS_C_H_
#define DRILL_MSGS__MSG__DETAIL__ROCK_TYPE__ROSIDL_TYPESUPPORT_FASTRTPS_C_H_


#include <stddef.h>
#include "rosidl_runtime_c/message_type_support_struct.h"
#include "rosidl_typesupport_interface/macros.h"
#include "drill_msgs/msg/rosidl_typesupport_fastrtps_c__visibility_control.h"
#include "drill_msgs/msg/detail/rock_type__struct.h"
#include "fastcdr/Cdr.h"

#ifdef __cplusplus
extern "C"
{
#endif

ROSIDL_TYPESUPPORT_FASTRTPS_C_PUBLIC_drill_msgs
bool cdr_serialize_drill_msgs__msg__RockType(
  const drill_msgs__msg__RockType * ros_message,
  eprosima::fastcdr::Cdr & cdr);

ROSIDL_TYPESUPPORT_FASTRTPS_C_PUBLIC_drill_msgs
bool cdr_deserialize_drill_msgs__msg__RockType(
  eprosima::fastcdr::Cdr &,
  drill_msgs__msg__RockType * ros_message);

ROSIDL_TYPESUPPORT_FASTRTPS_C_PUBLIC_drill_msgs
size_t get_serialized_size_drill_msgs__msg__RockType(
  const void * untyped_ros_message,
  size_t current_alignment);

ROSIDL_TYPESUPPORT_FASTRTPS_C_PUBLIC_drill_msgs
size_t max_serialized_size_drill_msgs__msg__RockType(
  bool & full_bounded,
  bool & is_plain,
  size_t current_alignment);

ROSIDL_TYPESUPPORT_FASTRTPS_C_PUBLIC_drill_msgs
bool cdr_serialize_key_drill_msgs__msg__RockType(
  const drill_msgs__msg__RockType * ros_message,
  eprosima::fastcdr::Cdr & cdr);

ROSIDL_TYPESUPPORT_FASTRTPS_C_PUBLIC_drill_msgs
size_t get_serialized_size_key_drill_msgs__msg__RockType(
  const void * untyped_ros_message,
  size_t current_alignment);

ROSIDL_TYPESUPPORT_FASTRTPS_C_PUBLIC_drill_msgs
size_t max_serialized_size_key_drill_msgs__msg__RockType(
  bool & full_bounded,
  bool & is_plain,
  size_t current_alignment);

ROSIDL_TYPESUPPORT_FASTRTPS_C_PUBLIC_drill_msgs
const rosidl_message_type_support_t *
ROSIDL_TYPESUPPORT_INTERFACE__MESSAGE_SYMBOL_NAME(rosidl_typesupport_fastrtps_c, drill_msgs, msg, RockType)();

#ifdef __cplusplus
}
#endif

#endif  // DRILL_MSGS__MSG__DETAIL__ROCK_TYPE__ROSIDL_TYPESUPPORT_FASTRTPS_C_H_
