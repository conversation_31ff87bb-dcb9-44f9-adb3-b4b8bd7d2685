// generated from rosidl_typesupport_fastrtps_c/resource/idl__type_support_c.cpp.em
// with input from drill_msgs:msg/ForkState.idl
// generated code does not contain a copyright notice
#include "drill_msgs/msg/detail/fork_state__rosidl_typesupport_fastrtps_c.h"


#include <cassert>
#include <cstddef>
#include <limits>
#include <string>
#include "rosidl_typesupport_fastrtps_c/identifier.h"
#include "rosidl_typesupport_fastrtps_c/serialization_helpers.hpp"
#include "rosidl_typesupport_fastrtps_c/wstring_conversion.hpp"
#include "rosidl_typesupport_fastrtps_cpp/message_type_support.h"
#include "drill_msgs/msg/rosidl_typesupport_fastrtps_c__visibility_control.h"
#include "drill_msgs/msg/detail/fork_state__struct.h"
#include "drill_msgs/msg/detail/fork_state__functions.h"
#include "fastcdr/Cdr.h"

#ifndef _WIN32
# pragma GCC diagnostic push
# pragma GCC diagnostic ignored "-Wunused-parameter"
# ifdef __clang__
#  pragma clang diagnostic ignored "-Wdeprecated-register"
#  pragma clang diagnostic ignored "-Wreturn-type-c-linkage"
# endif
#endif
#ifndef _WIN32
# pragma GCC diagnostic pop
#endif

// includes and forward declarations of message dependencies and their conversion functions

#if defined(__cplusplus)
extern "C"
{
#endif

#include "std_msgs/msg/detail/header__functions.h"  // header

// forward declare type support functions

ROSIDL_TYPESUPPORT_FASTRTPS_C_IMPORT_drill_msgs
bool cdr_serialize_std_msgs__msg__Header(
  const std_msgs__msg__Header * ros_message,
  eprosima::fastcdr::Cdr & cdr);

ROSIDL_TYPESUPPORT_FASTRTPS_C_IMPORT_drill_msgs
bool cdr_deserialize_std_msgs__msg__Header(
  eprosima::fastcdr::Cdr & cdr,
  std_msgs__msg__Header * ros_message);

ROSIDL_TYPESUPPORT_FASTRTPS_C_IMPORT_drill_msgs
size_t get_serialized_size_std_msgs__msg__Header(
  const void * untyped_ros_message,
  size_t current_alignment);

ROSIDL_TYPESUPPORT_FASTRTPS_C_IMPORT_drill_msgs
size_t max_serialized_size_std_msgs__msg__Header(
  bool & full_bounded,
  bool & is_plain,
  size_t current_alignment);

ROSIDL_TYPESUPPORT_FASTRTPS_C_IMPORT_drill_msgs
bool cdr_serialize_key_std_msgs__msg__Header(
  const std_msgs__msg__Header * ros_message,
  eprosima::fastcdr::Cdr & cdr);

ROSIDL_TYPESUPPORT_FASTRTPS_C_IMPORT_drill_msgs
size_t get_serialized_size_key_std_msgs__msg__Header(
  const void * untyped_ros_message,
  size_t current_alignment);

ROSIDL_TYPESUPPORT_FASTRTPS_C_IMPORT_drill_msgs
size_t max_serialized_size_key_std_msgs__msg__Header(
  bool & full_bounded,
  bool & is_plain,
  size_t current_alignment);

ROSIDL_TYPESUPPORT_FASTRTPS_C_IMPORT_drill_msgs
const rosidl_message_type_support_t *
  ROSIDL_TYPESUPPORT_INTERFACE__MESSAGE_SYMBOL_NAME(rosidl_typesupport_fastrtps_c, std_msgs, msg, Header)();


using _ForkState__ros_msg_type = drill_msgs__msg__ForkState;


ROSIDL_TYPESUPPORT_FASTRTPS_C_PUBLIC_drill_msgs
bool cdr_serialize_drill_msgs__msg__ForkState(
  const drill_msgs__msg__ForkState * ros_message,
  eprosima::fastcdr::Cdr & cdr)
{
  // Field name: header
  {
    cdr_serialize_std_msgs__msg__Header(
      &ros_message->header, cdr);
  }

  // Field name: extended
  {
    cdr << (ros_message->extended ? true : false);
  }

  // Field name: retracted
  {
    cdr << (ros_message->retracted ? true : false);
  }

  // Field name: cw_limit
  {
    cdr << (ros_message->cw_limit ? true : false);
  }

  // Field name: ccw_limit
  {
    cdr << (ros_message->ccw_limit ? true : false);
  }

  return true;
}

ROSIDL_TYPESUPPORT_FASTRTPS_C_PUBLIC_drill_msgs
bool cdr_deserialize_drill_msgs__msg__ForkState(
  eprosima::fastcdr::Cdr & cdr,
  drill_msgs__msg__ForkState * ros_message)
{
  // Field name: header
  {
    cdr_deserialize_std_msgs__msg__Header(cdr, &ros_message->header);
  }

  // Field name: extended
  {
    uint8_t tmp;
    cdr >> tmp;
    ros_message->extended = tmp ? true : false;
  }

  // Field name: retracted
  {
    uint8_t tmp;
    cdr >> tmp;
    ros_message->retracted = tmp ? true : false;
  }

  // Field name: cw_limit
  {
    uint8_t tmp;
    cdr >> tmp;
    ros_message->cw_limit = tmp ? true : false;
  }

  // Field name: ccw_limit
  {
    uint8_t tmp;
    cdr >> tmp;
    ros_message->ccw_limit = tmp ? true : false;
  }

  return true;
}  // NOLINT(readability/fn_size)


ROSIDL_TYPESUPPORT_FASTRTPS_C_PUBLIC_drill_msgs
size_t get_serialized_size_drill_msgs__msg__ForkState(
  const void * untyped_ros_message,
  size_t current_alignment)
{
  const _ForkState__ros_msg_type * ros_message = static_cast<const _ForkState__ros_msg_type *>(untyped_ros_message);
  (void)ros_message;
  size_t initial_alignment = current_alignment;

  const size_t padding = 4;
  const size_t wchar_size = 4;
  (void)padding;
  (void)wchar_size;

  // Field name: header
  current_alignment += get_serialized_size_std_msgs__msg__Header(
    &(ros_message->header), current_alignment);

  // Field name: extended
  {
    size_t item_size = sizeof(ros_message->extended);
    current_alignment += item_size +
      eprosima::fastcdr::Cdr::alignment(current_alignment, item_size);
  }

  // Field name: retracted
  {
    size_t item_size = sizeof(ros_message->retracted);
    current_alignment += item_size +
      eprosima::fastcdr::Cdr::alignment(current_alignment, item_size);
  }

  // Field name: cw_limit
  {
    size_t item_size = sizeof(ros_message->cw_limit);
    current_alignment += item_size +
      eprosima::fastcdr::Cdr::alignment(current_alignment, item_size);
  }

  // Field name: ccw_limit
  {
    size_t item_size = sizeof(ros_message->ccw_limit);
    current_alignment += item_size +
      eprosima::fastcdr::Cdr::alignment(current_alignment, item_size);
  }

  return current_alignment - initial_alignment;
}


ROSIDL_TYPESUPPORT_FASTRTPS_C_PUBLIC_drill_msgs
size_t max_serialized_size_drill_msgs__msg__ForkState(
  bool & full_bounded,
  bool & is_plain,
  size_t current_alignment)
{
  size_t initial_alignment = current_alignment;

  const size_t padding = 4;
  const size_t wchar_size = 4;
  size_t last_member_size = 0;
  (void)last_member_size;
  (void)padding;
  (void)wchar_size;

  full_bounded = true;
  is_plain = true;

  // Field name: header
  {
    size_t array_size = 1;
    last_member_size = 0;
    for (size_t index = 0; index < array_size; ++index) {
      bool inner_full_bounded;
      bool inner_is_plain;
      size_t inner_size;
      inner_size =
        max_serialized_size_std_msgs__msg__Header(
        inner_full_bounded, inner_is_plain, current_alignment);
      last_member_size += inner_size;
      current_alignment += inner_size;
      full_bounded &= inner_full_bounded;
      is_plain &= inner_is_plain;
    }
  }

  // Field name: extended
  {
    size_t array_size = 1;
    last_member_size = array_size * sizeof(uint8_t);
    current_alignment += array_size * sizeof(uint8_t);
  }

  // Field name: retracted
  {
    size_t array_size = 1;
    last_member_size = array_size * sizeof(uint8_t);
    current_alignment += array_size * sizeof(uint8_t);
  }

  // Field name: cw_limit
  {
    size_t array_size = 1;
    last_member_size = array_size * sizeof(uint8_t);
    current_alignment += array_size * sizeof(uint8_t);
  }

  // Field name: ccw_limit
  {
    size_t array_size = 1;
    last_member_size = array_size * sizeof(uint8_t);
    current_alignment += array_size * sizeof(uint8_t);
  }


  size_t ret_val = current_alignment - initial_alignment;
  if (is_plain) {
    // All members are plain, and type is not empty.
    // We still need to check that the in-memory alignment
    // is the same as the CDR mandated alignment.
    using DataType = drill_msgs__msg__ForkState;
    is_plain =
      (
      offsetof(DataType, ccw_limit) +
      last_member_size
      ) == ret_val;
  }
  return ret_val;
}

ROSIDL_TYPESUPPORT_FASTRTPS_C_PUBLIC_drill_msgs
bool cdr_serialize_key_drill_msgs__msg__ForkState(
  const drill_msgs__msg__ForkState * ros_message,
  eprosima::fastcdr::Cdr & cdr)
{
  // Field name: header
  {
    cdr_serialize_key_std_msgs__msg__Header(
      &ros_message->header, cdr);
  }

  // Field name: extended
  {
    cdr << (ros_message->extended ? true : false);
  }

  // Field name: retracted
  {
    cdr << (ros_message->retracted ? true : false);
  }

  // Field name: cw_limit
  {
    cdr << (ros_message->cw_limit ? true : false);
  }

  // Field name: ccw_limit
  {
    cdr << (ros_message->ccw_limit ? true : false);
  }

  return true;
}

ROSIDL_TYPESUPPORT_FASTRTPS_C_PUBLIC_drill_msgs
size_t get_serialized_size_key_drill_msgs__msg__ForkState(
  const void * untyped_ros_message,
  size_t current_alignment)
{
  const _ForkState__ros_msg_type * ros_message = static_cast<const _ForkState__ros_msg_type *>(untyped_ros_message);
  (void)ros_message;

  size_t initial_alignment = current_alignment;

  const size_t padding = 4;
  const size_t wchar_size = 4;
  (void)padding;
  (void)wchar_size;

  // Field name: header
  current_alignment += get_serialized_size_key_std_msgs__msg__Header(
    &(ros_message->header), current_alignment);

  // Field name: extended
  {
    size_t item_size = sizeof(ros_message->extended);
    current_alignment += item_size +
      eprosima::fastcdr::Cdr::alignment(current_alignment, item_size);
  }

  // Field name: retracted
  {
    size_t item_size = sizeof(ros_message->retracted);
    current_alignment += item_size +
      eprosima::fastcdr::Cdr::alignment(current_alignment, item_size);
  }

  // Field name: cw_limit
  {
    size_t item_size = sizeof(ros_message->cw_limit);
    current_alignment += item_size +
      eprosima::fastcdr::Cdr::alignment(current_alignment, item_size);
  }

  // Field name: ccw_limit
  {
    size_t item_size = sizeof(ros_message->ccw_limit);
    current_alignment += item_size +
      eprosima::fastcdr::Cdr::alignment(current_alignment, item_size);
  }

  return current_alignment - initial_alignment;
}

ROSIDL_TYPESUPPORT_FASTRTPS_C_PUBLIC_drill_msgs
size_t max_serialized_size_key_drill_msgs__msg__ForkState(
  bool & full_bounded,
  bool & is_plain,
  size_t current_alignment)
{
  size_t initial_alignment = current_alignment;

  const size_t padding = 4;
  const size_t wchar_size = 4;
  size_t last_member_size = 0;
  (void)last_member_size;
  (void)padding;
  (void)wchar_size;

  full_bounded = true;
  is_plain = true;
  // Field name: header
  {
    size_t array_size = 1;
    last_member_size = 0;
    for (size_t index = 0; index < array_size; ++index) {
      bool inner_full_bounded;
      bool inner_is_plain;
      size_t inner_size;
      inner_size =
        max_serialized_size_key_std_msgs__msg__Header(
        inner_full_bounded, inner_is_plain, current_alignment);
      last_member_size += inner_size;
      current_alignment += inner_size;
      full_bounded &= inner_full_bounded;
      is_plain &= inner_is_plain;
    }
  }

  // Field name: extended
  {
    size_t array_size = 1;
    last_member_size = array_size * sizeof(uint8_t);
    current_alignment += array_size * sizeof(uint8_t);
  }

  // Field name: retracted
  {
    size_t array_size = 1;
    last_member_size = array_size * sizeof(uint8_t);
    current_alignment += array_size * sizeof(uint8_t);
  }

  // Field name: cw_limit
  {
    size_t array_size = 1;
    last_member_size = array_size * sizeof(uint8_t);
    current_alignment += array_size * sizeof(uint8_t);
  }

  // Field name: ccw_limit
  {
    size_t array_size = 1;
    last_member_size = array_size * sizeof(uint8_t);
    current_alignment += array_size * sizeof(uint8_t);
  }

  size_t ret_val = current_alignment - initial_alignment;
  if (is_plain) {
    // All members are plain, and type is not empty.
    // We still need to check that the in-memory alignment
    // is the same as the CDR mandated alignment.
    using DataType = drill_msgs__msg__ForkState;
    is_plain =
      (
      offsetof(DataType, ccw_limit) +
      last_member_size
      ) == ret_val;
  }
  return ret_val;
}


static bool _ForkState__cdr_serialize(
  const void * untyped_ros_message,
  eprosima::fastcdr::Cdr & cdr)
{
  if (!untyped_ros_message) {
    fprintf(stderr, "ros message handle is null\n");
    return false;
  }
  const drill_msgs__msg__ForkState * ros_message = static_cast<const drill_msgs__msg__ForkState *>(untyped_ros_message);
  (void)ros_message;
  return cdr_serialize_drill_msgs__msg__ForkState(ros_message, cdr);
}

static bool _ForkState__cdr_deserialize(
  eprosima::fastcdr::Cdr & cdr,
  void * untyped_ros_message)
{
  if (!untyped_ros_message) {
    fprintf(stderr, "ros message handle is null\n");
    return false;
  }
  drill_msgs__msg__ForkState * ros_message = static_cast<drill_msgs__msg__ForkState *>(untyped_ros_message);
  (void)ros_message;
  return cdr_deserialize_drill_msgs__msg__ForkState(cdr, ros_message);
}

static uint32_t _ForkState__get_serialized_size(const void * untyped_ros_message)
{
  return static_cast<uint32_t>(
    get_serialized_size_drill_msgs__msg__ForkState(
      untyped_ros_message, 0));
}

static size_t _ForkState__max_serialized_size(char & bounds_info)
{
  bool full_bounded;
  bool is_plain;
  size_t ret_val;

  ret_val = max_serialized_size_drill_msgs__msg__ForkState(
    full_bounded, is_plain, 0);

  bounds_info =
    is_plain ? ROSIDL_TYPESUPPORT_FASTRTPS_PLAIN_TYPE :
    full_bounded ? ROSIDL_TYPESUPPORT_FASTRTPS_BOUNDED_TYPE : ROSIDL_TYPESUPPORT_FASTRTPS_UNBOUNDED_TYPE;
  return ret_val;
}


static message_type_support_callbacks_t __callbacks_ForkState = {
  "drill_msgs::msg",
  "ForkState",
  _ForkState__cdr_serialize,
  _ForkState__cdr_deserialize,
  _ForkState__get_serialized_size,
  _ForkState__max_serialized_size,
  nullptr
};

static rosidl_message_type_support_t _ForkState__type_support = {
  rosidl_typesupport_fastrtps_c__identifier,
  &__callbacks_ForkState,
  get_message_typesupport_handle_function,
  &drill_msgs__msg__ForkState__get_type_hash,
  &drill_msgs__msg__ForkState__get_type_description,
  &drill_msgs__msg__ForkState__get_type_description_sources,
};

const rosidl_message_type_support_t *
ROSIDL_TYPESUPPORT_INTERFACE__MESSAGE_SYMBOL_NAME(rosidl_typesupport_fastrtps_c, drill_msgs, msg, ForkState)() {
  return &_ForkState__type_support;
}

#if defined(__cplusplus)
}
#endif
