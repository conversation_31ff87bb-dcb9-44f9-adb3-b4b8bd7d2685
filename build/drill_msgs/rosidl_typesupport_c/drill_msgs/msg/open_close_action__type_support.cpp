// generated from rosidl_typesupport_c/resource/idl__type_support.cpp.em
// with input from drill_msgs:msg/OpenCloseAction.idl
// generated code does not contain a copyright notice

#include "cstddef"
#include "rosidl_runtime_c/message_type_support_struct.h"
#include "drill_msgs/msg/detail/open_close_action__struct.h"
#include "drill_msgs/msg/detail/open_close_action__type_support.h"
#include "drill_msgs/msg/detail/open_close_action__functions.h"
#include "rosidl_typesupport_c/identifier.h"
#include "rosidl_typesupport_c/message_type_support_dispatch.h"
#include "rosidl_typesupport_c/type_support_map.h"
#include "rosidl_typesupport_c/visibility_control.h"
#include "rosidl_typesupport_interface/macros.h"

namespace drill_msgs
{

namespace msg
{

namespace rosidl_typesupport_c
{

typedef struct _OpenCloseAction_type_support_ids_t
{
  const char * typesupport_identifier[2];
} _OpenCloseAction_type_support_ids_t;

static const _OpenCloseAction_type_support_ids_t _OpenCloseAction_message_typesupport_ids = {
  {
    "rosidl_typesupport_fastrtps_c",  // ::rosidl_typesupport_fastrtps_c::typesupport_identifier,
    "rosidl_typesupport_introspection_c",  // ::rosidl_typesupport_introspection_c::typesupport_identifier,
  }
};

typedef struct _OpenCloseAction_type_support_symbol_names_t
{
  const char * symbol_name[2];
} _OpenCloseAction_type_support_symbol_names_t;

#define STRINGIFY_(s) #s
#define STRINGIFY(s) STRINGIFY_(s)

static const _OpenCloseAction_type_support_symbol_names_t _OpenCloseAction_message_typesupport_symbol_names = {
  {
    STRINGIFY(ROSIDL_TYPESUPPORT_INTERFACE__MESSAGE_SYMBOL_NAME(rosidl_typesupport_fastrtps_c, drill_msgs, msg, OpenCloseAction)),
    STRINGIFY(ROSIDL_TYPESUPPORT_INTERFACE__MESSAGE_SYMBOL_NAME(rosidl_typesupport_introspection_c, drill_msgs, msg, OpenCloseAction)),
  }
};

typedef struct _OpenCloseAction_type_support_data_t
{
  void * data[2];
} _OpenCloseAction_type_support_data_t;

static _OpenCloseAction_type_support_data_t _OpenCloseAction_message_typesupport_data = {
  {
    0,  // will store the shared library later
    0,  // will store the shared library later
  }
};

static const type_support_map_t _OpenCloseAction_message_typesupport_map = {
  2,
  "drill_msgs",
  &_OpenCloseAction_message_typesupport_ids.typesupport_identifier[0],
  &_OpenCloseAction_message_typesupport_symbol_names.symbol_name[0],
  &_OpenCloseAction_message_typesupport_data.data[0],
};

static const rosidl_message_type_support_t OpenCloseAction_message_type_support_handle = {
  rosidl_typesupport_c__typesupport_identifier,
  reinterpret_cast<const type_support_map_t *>(&_OpenCloseAction_message_typesupport_map),
  rosidl_typesupport_c__get_message_typesupport_handle_function,
  &drill_msgs__msg__OpenCloseAction__get_type_hash,
  &drill_msgs__msg__OpenCloseAction__get_type_description,
  &drill_msgs__msg__OpenCloseAction__get_type_description_sources,
};

}  // namespace rosidl_typesupport_c

}  // namespace msg

}  // namespace drill_msgs

#ifdef __cplusplus
extern "C"
{
#endif

const rosidl_message_type_support_t *
ROSIDL_TYPESUPPORT_INTERFACE__MESSAGE_SYMBOL_NAME(rosidl_typesupport_c, drill_msgs, msg, OpenCloseAction)() {
  return &::drill_msgs::msg::rosidl_typesupport_c::OpenCloseAction_message_type_support_handle;
}

#ifdef __cplusplus
}
#endif
