# CMAKE generated file: DO NOT EDIT!
# Generated by "Unix Makefiles" Generator, CMake Version 4.1

# compile C with /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/cc
C_DEFINES = -DROSIDL_GENERATOR_C_BUILDING_DLL_can_msgs -DROS_PACKAGE_NAME=\"can_msgs\"

C_INCLUDES = -I/Users/<USER>/Work/drill2/onboard/build/can_msgs/rosidl_generator_c -isystem /Users/<USER>/ros2_jazzy/install/std_msgs/include/std_msgs -isystem /Users/<USER>/ros2_jazzy/install/builtin_interfaces/include/builtin_interfaces -isystem /Users/<USER>/ros2_jazzy/install/rosidl_runtime_c/include/rosidl_runtime_c -isystem /Users/<USER>/ros2_jazzy/install/rcutils/include/rcutils -isystem /Users/<USER>/ros2_jazzy/install/rosidl_typesupport_interface/include/rosidl_typesupport_interface

C_FLAGSarm64 = -std=gnu11 -arch arm64 -isysroot /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk -fPIC -Wall

C_FLAGS = -std=gnu11 -arch arm64 -isysroot /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk -fPIC -Wall

