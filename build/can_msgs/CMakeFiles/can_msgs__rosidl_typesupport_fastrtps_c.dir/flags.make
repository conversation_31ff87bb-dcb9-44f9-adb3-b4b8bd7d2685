# CMAKE generated file: DO NOT EDIT!
# Generated by "Unix Makefiles" Generator, CMake Version 4.1

# compile CXX with /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/c++
CXX_DEFINES = -DFASTCDR_DYN_LINK -DROSIDL_TYPESUPPORT_FASTRTPS_C_BUILDING_DLL_can_msgs -DROS_PACKAGE_NAME=\"can_msgs\"

CXX_INCLUDES = -I/Users/<USER>/Work/drill2/onboard/build/can_msgs/rosidl_typesupport_fastrtps_c -I/Users/<USER>/Work/drill2/onboard/build/can_msgs/rosidl_generator_c -isystem /Users/<USER>/ros2_jazzy/install/fastcdr/include -isystem /Users/<USER>/ros2_jazzy/install/rosidl_runtime_c/include/rosidl_runtime_c -isystem /Users/<USER>/ros2_jazzy/install/rcutils/include/rcutils -isystem /Users/<USER>/ros2_jazzy/install/rosidl_typesupport_interface/include/rosidl_typesupport_interface -isystem /Users/<USER>/ros2_jazzy/install/rosidl_runtime_cpp/include/rosidl_runtime_cpp -isystem /Users/<USER>/ros2_jazzy/install/rosidl_typesupport_fastrtps_cpp/include/rosidl_typesupport_fastrtps_cpp -isystem /Users/<USER>/ros2_jazzy/install/rmw/include/rmw -isystem /Users/<USER>/ros2_jazzy/install/rosidl_dynamic_typesupport/include/rosidl_dynamic_typesupport -isystem /Users/<USER>/ros2_jazzy/install/rosidl_typesupport_fastrtps_c/include/rosidl_typesupport_fastrtps_c -isystem /Users/<USER>/ros2_jazzy/install/std_msgs/include/std_msgs -isystem /Users/<USER>/ros2_jazzy/install/builtin_interfaces/include/builtin_interfaces

CXX_FLAGSarm64 = -I/opt/homebrew/include -std=gnu++17 -arch arm64 -isysroot /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk -fPIC -Wall -Wextra -Wpedantic

CXX_FLAGS = -I/opt/homebrew/include -std=gnu++17 -arch arm64 -isysroot /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk -fPIC -Wall -Wextra -Wpedantic

