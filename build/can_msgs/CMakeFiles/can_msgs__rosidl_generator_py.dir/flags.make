# CMAKE generated file: DO NOT EDIT!
# Generated by "Unix Makefiles" Generator, CMake Version 4.1

# compile C with /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/cc
C_DEFINES = -DFASTCDR_DYN_LINK -DROS_PACKAGE_NAME=\"can_msgs\" -Dcan_msgs__rosidl_generator_py_EXPORTS

C_INCLUDES = -I/Users/<USER>/Work/drill2/onboard/build/can_msgs/rosidl_generator_c -I/Users/<USER>/Work/drill2/onboard/build/can_msgs/rosidl_generator_py -isystem /Users/<USER>/ros2_jazzy/install/std_msgs/include/std_msgs -isystem /Users/<USER>/ros2_jazzy/install/builtin_interfaces/include/builtin_interfaces -isystem /Users/<USER>/ros2_jazzy/install/rosidl_runtime_c/include/rosidl_runtime_c -isystem /Users/<USER>/ros2_jazzy/install/rcutils/include/rcutils -isystem /Users/<USER>/ros2_jazzy/install/rosidl_typesupport_interface/include/rosidl_typesupport_interface -isystem /Users/<USER>/.ros2_venv/lib/python3.11/site-packages/numpy/_core/include -isystem /opt/homebrew/opt/python@3.11/Frameworks/Python.framework/Versions/3.11/include/python3.11 -isystem /Users/<USER>/ros2_jazzy/install/fastcdr/include -isystem /Users/<USER>/ros2_jazzy/install/rosidl_runtime_cpp/include/rosidl_runtime_cpp -isystem /Users/<USER>/ros2_jazzy/install/rosidl_typesupport_fastrtps_cpp/include/rosidl_typesupport_fastrtps_cpp -isystem /Users/<USER>/ros2_jazzy/install/rmw/include/rmw -isystem /Users/<USER>/ros2_jazzy/install/rosidl_dynamic_typesupport/include/rosidl_dynamic_typesupport -isystem /Users/<USER>/ros2_jazzy/install/rosidl_typesupport_fastrtps_c/include/rosidl_typesupport_fastrtps_c -isystem /Users/<USER>/ros2_jazzy/install/rosidl_typesupport_introspection_c/include/rosidl_typesupport_introspection_c -isystem /Users/<USER>/ros2_jazzy/install/rosidl_typesupport_introspection_cpp/include/rosidl_typesupport_introspection_cpp

C_FLAGSarm64 =  -arch arm64 -isysroot /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk -fPIC -Wall -Wextra

C_FLAGS =  -arch arm64 -isysroot /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk -fPIC -Wall -Wextra

