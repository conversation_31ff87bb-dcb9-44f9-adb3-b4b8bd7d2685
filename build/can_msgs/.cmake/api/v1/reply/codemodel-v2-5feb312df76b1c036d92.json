{"configurations": [{"directories": [{"build": ".", "childIndexes": [1], "hasInstallRule": true, "jsonFile": "directory-.-76a56003a14dd3eb99a6.json", "minimumCMakeVersion": {"string": "3.20"}, "projectIndex": 0, "source": ".", "targetIndexes": [0, 1, 2, 3, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18]}, {"build": "can_msgs__py", "jsonFile": "directory-can_msgs__py-38281a7bc372c02da67c.json", "minimumCMakeVersion": {"string": "3.20"}, "parentIndex": 0, "projectIndex": 0, "source": "/Users/<USER>/Work/drill2/onboard/build/can_msgs/can_msgs__py", "targetIndexes": [4]}], "name": "", "projects": [{"directoryIndexes": [0, 1], "name": "can_msgs", "targetIndexes": [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18]}], "targets": [{"directoryIndex": 0, "id": "ament_cmake_python_build_can_msgs_egg::@6890427a1f51a3e7e1df", "jsonFile": "target-ament_cmake_python_build_can_msgs_egg-eeb3246af1eef19953ef.json", "name": "ament_cmake_python_build_can_msgs_egg", "projectIndex": 0}, {"directoryIndex": 0, "id": "ament_cmake_python_symlink_can_msgs::@6890427a1f51a3e7e1df", "jsonFile": "target-ament_cmake_python_symlink_can_msgs-6c650cc9027853f6a165.json", "name": "ament_cmake_python_symlink_can_msgs", "projectIndex": 0}, {"directoryIndex": 0, "id": "can_msgs::@6890427a1f51a3e7e1df", "jsonFile": "target-can_msgs-10f3afaf2dc3ab513c1f.json", "name": "can_msgs", "projectIndex": 0}, {"directoryIndex": 0, "id": "can_msgs__cpp::@6890427a1f51a3e7e1df", "jsonFile": "target-can_msgs__cpp-e10a4f99b927e8029df3.json", "name": "can_msgs__cpp", "projectIndex": 0}, {"directoryIndex": 1, "id": "can_msgs__py::@fa8439080ea556237913", "jsonFile": "target-can_msgs__py-d96f69b8f35731176dd8.json", "name": "can_msgs__py", "projectIndex": 0}, {"directoryIndex": 0, "id": "can_msgs__rosidl_generator_c::@6890427a1f51a3e7e1df", "jsonFile": "target-can_msgs__rosidl_generator_c-0aecd682b68a5f92f0e0.json", "name": "can_msgs__rosidl_generator_c", "projectIndex": 0}, {"directoryIndex": 0, "id": "can_msgs__rosidl_generator_py::@6890427a1f51a3e7e1df", "jsonFile": "target-can_msgs__rosidl_generator_py-6ddafaa34ae0ee11f381.json", "name": "can_msgs__rosidl_generator_py", "projectIndex": 0}, {"directoryIndex": 0, "id": "can_msgs__rosidl_generator_type_description::@6890427a1f51a3e7e1df", "jsonFile": "target-can_msgs__rosidl_generator_type_description-def2dbba501c01f10dd5.json", "name": "can_msgs__rosidl_generator_type_description", "projectIndex": 0}, {"directoryIndex": 0, "id": "can_msgs__rosidl_typesupport_c::@6890427a1f51a3e7e1df", "jsonFile": "target-can_msgs__rosidl_typesupport_c-4041f017abdea02b2e7b.json", "name": "can_msgs__rosidl_typesupport_c", "projectIndex": 0}, {"directoryIndex": 0, "id": "can_msgs__rosidl_typesupport_cpp::@6890427a1f51a3e7e1df", "jsonFile": "target-can_msgs__rosidl_typesupport_cpp-6a4711407d12434d3d56.json", "name": "can_msgs__rosidl_typesupport_cpp", "projectIndex": 0}, {"directoryIndex": 0, "id": "can_msgs__rosidl_typesupport_fastrtps_c::@6890427a1f51a3e7e1df", "jsonFile": "target-can_msgs__rosidl_typesupport_fastrtps_c-fdd35d2f6572481b1bc0.json", "name": "can_msgs__rosidl_typesupport_fastrtps_c", "projectIndex": 0}, {"directoryIndex": 0, "id": "can_msgs__rosidl_typesupport_fastrtps_cpp::@6890427a1f51a3e7e1df", "jsonFile": "target-can_msgs__rosidl_typesupport_fastrtps_cpp-d50360a4d95bfc944b14.json", "name": "can_msgs__rosidl_typesupport_fastrtps_cpp", "projectIndex": 0}, {"directoryIndex": 0, "id": "can_msgs__rosidl_typesupport_introspection_c::@6890427a1f51a3e7e1df", "jsonFile": "target-can_msgs__rosidl_typesupport_introspection_c-7e49404b6e7b4b74d03f.json", "name": "can_msgs__rosidl_typesupport_introspection_c", "projectIndex": 0}, {"directoryIndex": 0, "id": "can_msgs__rosidl_typesupport_introspection_cpp::@6890427a1f51a3e7e1df", "jsonFile": "target-can_msgs__rosidl_typesupport_introspection_cpp-a09c8f968812ac5ad875.json", "name": "can_msgs__rosidl_typesupport_introspection_cpp", "projectIndex": 0}, {"directoryIndex": 0, "id": "can_msgs_s__rosidl_typesupport_c::@6890427a1f51a3e7e1df", "jsonFile": "target-can_msgs_s__rosidl_typesupport_c-478122cea8cd83b9136f.json", "name": "can_msgs_s__rosidl_typesupport_c", "projectIndex": 0}, {"directoryIndex": 0, "id": "can_msgs_s__rosidl_typesupport_fastrtps_c::@6890427a1f51a3e7e1df", "jsonFile": "target-can_msgs_s__rosidl_typesupport_fastrtps_c-d4e5b9ef00b0aea78c74.json", "name": "can_msgs_s__rosidl_typesupport_fastrtps_c", "projectIndex": 0}, {"directoryIndex": 0, "id": "can_msgs_s__rosidl_typesupport_introspection_c::@6890427a1f51a3e7e1df", "jsonFile": "target-can_msgs_s__rosidl_typesupport_introspection_c-b2615d1dcf5ae3b1d9cc.json", "name": "can_msgs_s__rosidl_typesupport_introspection_c", "projectIndex": 0}, {"directoryIndex": 0, "id": "can_msgs_uninstall::@6890427a1f51a3e7e1df", "jsonFile": "target-can_msgs_uninstall-7fe5cc33241e823a64e8.json", "name": "can_msgs_uninstall", "projectIndex": 0}, {"directoryIndex": 0, "id": "uninstall::@6890427a1f51a3e7e1df", "jsonFile": "target-uninstall-e9a61349a87de60c5ebb.json", "name": "uninstall", "projectIndex": 0}]}], "kind": "codemodel", "paths": {"build": "/Users/<USER>/Work/drill2/onboard/build/can_msgs", "source": "/Users/<USER>/Work/drill2/onboard/src/can_msgs"}, "version": {"major": 2, "minor": 8}}