{"artifacts": [{"path": "libcan_msgs__rosidl_generator_py.dylib"}], "backtrace": 5, "backtraceGraph": {"commands": ["add_library", "include", "ament_execute_extensions", "rosidl_generate_interfaces", "_install", "ament_cmake_symlink_install_targets", "install", "target_link_libraries", "set_target_properties", "find_package", "add_dependencies", "add_definitions", "target_include_directories"], "files": ["/Users/<USER>/ros2_jazzy/install/rosidl_generator_py/share/rosidl_generator_py/cmake/rosidl_generator_py_generate_interfaces.cmake", "/Users/<USER>/ros2_jazzy/install/ament_cmake_core/share/ament_cmake_core/cmake/core/ament_execute_extensions.cmake", "/Users/<USER>/ros2_jazzy/install/rosidl_cmake/share/rosidl_cmake/cmake/rosidl_generate_interfaces.cmake", "CMakeLists.txt", "/Users/<USER>/ros2_jazzy/install/ament_cmake_core/share/ament_cmake_core/cmake/symlink_install/ament_cmake_symlink_install_targets.cmake", "/Users/<USER>/ros2_jazzy/install/ament_cmake_core/share/ament_cmake_core/cmake/symlink_install/install.cmake", "/Users/<USER>/ros2_jazzy/install/std_msgs/share/std_msgs/cmake/export_std_msgs__rosidl_typesupport_fastrtps_cExport.cmake", "/Users/<USER>/ros2_jazzy/install/std_msgs/share/std_msgs/cmake/ament_cmake_export_targets-extras.cmake", "/Users/<USER>/ros2_jazzy/install/std_msgs/share/std_msgs/cmake/std_msgsConfig.cmake", "/Users/<USER>/ros2_jazzy/install/std_msgs/share/std_msgs/cmake/std_msgs__rosidl_typesupport_introspection_cppExport.cmake", "/Users/<USER>/ros2_jazzy/install/std_msgs/share/std_msgs/cmake/std_msgs__rosidl_typesupport_introspection_cExport.cmake", "/Users/<USER>/ros2_jazzy/install/std_msgs/share/std_msgs/cmake/export_std_msgs__rosidl_typesupport_fastrtps_cppExport.cmake", "/Users/<USER>/ros2_jazzy/install/rmw/share/rmw/cmake/rmwExport.cmake", "/Users/<USER>/ros2_jazzy/install/rmw/share/rmw/cmake/ament_cmake_export_targets-extras.cmake", "/Users/<USER>/ros2_jazzy/install/rmw/share/rmw/cmake/rmwConfig.cmake", "/Users/<USER>/ros2_jazzy/install/rosidl_typesupport_fastrtps_cpp/share/rosidl_typesupport_fastrtps_cpp/cmake/ament_cmake_export_dependencies-extras.cmake", "/Users/<USER>/ros2_jazzy/install/rosidl_typesupport_fastrtps_cpp/share/rosidl_typesupport_fastrtps_cpp/cmake/rosidl_typesupport_fastrtps_cppConfig.cmake", "/Users/<USER>/ros2_jazzy/install/rosidl_typesupport_cpp/share/rosidl_typesupport_cpp/cmake/rosidl_typesupport_cpp-extras.cmake", "/Users/<USER>/ros2_jazzy/install/rosidl_typesupport_cpp/share/rosidl_typesupport_cpp/cmake/rosidl_typesupport_cppConfig.cmake", "/Users/<USER>/ros2_jazzy/install/rosidl_core_generators/share/rosidl_core_generators/cmake/rosidl_core_generators-extras.cmake", "/Users/<USER>/ros2_jazzy/install/rosidl_core_generators/share/rosidl_core_generators/cmake/rosidl_core_generatorsConfig.cmake", "/Users/<USER>/ros2_jazzy/install/rosidl_default_generators/share/rosidl_default_generators/cmake/ament_cmake_export_dependencies-extras.cmake", "/Users/<USER>/ros2_jazzy/install/rosidl_default_generators/share/rosidl_default_generators/cmake/rosidl_default_generatorsConfig.cmake", "/Users/<USER>/ros2_jazzy/install/rosidl_generator_c/share/rosidl_generator_c/cmake/rosidl_generator_c_generate_interfaces.cmake", "/Users/<USER>/ros2_jazzy/install/ament_cmake_ros/share/ament_cmake_ros/cmake/ament_cmake_ros-extras.cmake", "/Users/<USER>/ros2_jazzy/install/ament_cmake_ros/share/ament_cmake_ros/cmake/ament_cmake_rosConfig.cmake", "/Users/<USER>/ros2_jazzy/install/rosidl_typesupport_fastrtps_c/share/rosidl_typesupport_fastrtps_c/cmake/rosidl_typesupport_fastrtps_c_generate_interfaces.cmake"], "nodes": [{"file": 3}, {"command": 3, "file": 3, "line": 25, "parent": 0}, {"command": 2, "file": 2, "line": 280, "parent": 1}, {"command": 1, "file": 1, "line": 48, "parent": 2}, {"file": 0, "parent": 3}, {"command": 0, "file": 0, "line": 140, "parent": 4}, {"command": 6, "file": 0, "line": 240, "parent": 4}, {"command": 5, "file": 5, "line": 37, "parent": 6}, {"command": 4, "file": 4, "line": 50, "parent": 7}, {"command": 7, "file": 0, "line": 149, "parent": 4}, {"command": 7, "file": 0, "line": 166, "parent": 4}, {"command": 7, "file": 0, "line": 234, "parent": 4}, {"command": 7, "file": 0, "line": 234, "parent": 4}, {"command": 7, "file": 0, "line": 141, "parent": 4}, {"command": 9, "file": 3, "line": 15, "parent": 0}, {"file": 8, "parent": 14}, {"command": 1, "file": 8, "line": 41, "parent": 15}, {"file": 7, "parent": 16}, {"command": 1, "file": 7, "line": 9, "parent": 17}, {"file": 6, "parent": 18}, {"command": 8, "file": 6, "line": 61, "parent": 19}, {"command": 1, "file": 7, "line": 9, "parent": 17}, {"file": 9, "parent": 21}, {"command": 8, "file": 9, "line": 61, "parent": 22}, {"command": 1, "file": 7, "line": 9, "parent": 17}, {"file": 10, "parent": 24}, {"command": 8, "file": 10, "line": 61, "parent": 25}, {"command": 1, "file": 7, "line": 9, "parent": 17}, {"file": 11, "parent": 27}, {"command": 8, "file": 11, "line": 61, "parent": 28}, {"command": 9, "file": 3, "line": 14, "parent": 0}, {"file": 22, "parent": 30}, {"command": 1, "file": 22, "line": 41, "parent": 31}, {"file": 21, "parent": 32}, {"command": 9, "file": 21, "line": 21, "parent": 33}, {"file": 20, "parent": 34}, {"command": 1, "file": 20, "line": 41, "parent": 35}, {"file": 19, "parent": 36}, {"command": 9, "file": 19, "line": 21, "parent": 37}, {"file": 18, "parent": 38}, {"command": 1, "file": 18, "line": 41, "parent": 39}, {"file": 17, "parent": 40}, {"command": 9, "file": 17, "line": 13, "parent": 41}, {"file": 16, "parent": 42}, {"command": 1, "file": 16, "line": 41, "parent": 43}, {"file": 15, "parent": 44}, {"command": 9, "file": 15, "line": 21, "parent": 45}, {"file": 14, "parent": 46}, {"command": 1, "file": 14, "line": 41, "parent": 47}, {"file": 13, "parent": 48}, {"command": 1, "file": 13, "line": 9, "parent": 49}, {"file": 12, "parent": 50}, {"command": 8, "file": 12, "line": 61, "parent": 51}, {"command": 1, "file": 1, "line": 48, "parent": 2}, {"file": 23, "parent": 53}, {"command": 7, "file": 23, "line": 170, "parent": 54}, {"command": 10, "file": 0, "line": 143, "parent": 4}, {"command": 8, "file": 0, "line": 237, "parent": 4}, {"command": 1, "file": 1, "line": 48, "parent": 2}, {"file": 26, "parent": 58}, {"command": 9, "file": 26, "line": 21, "parent": 59}, {"file": 25, "parent": 60}, {"command": 1, "file": 25, "line": 41, "parent": 61}, {"file": 24, "parent": 62}, {"command": 11, "file": 24, "line": 25, "parent": 63}, {"command": 12, "file": 0, "line": 154, "parent": 4}]}, "compileGroups": [{"compileCommandFragments": [{"fragment": " -arch arm64 -isysroot /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk -fPIC"}, {"backtrace": 57, "fragment": "-Wall"}, {"backtrace": 57, "fragment": "-Wextra"}], "defines": [{"backtrace": 11, "define": "FASTCDR_DYN_LINK"}, {"backtrace": 64, "define": "ROS_PACKAGE_NAME=\"can_msgs\""}, {"define": "can_msgs__rosidl_generator_py_EXPORTS"}], "includes": [{"backtrace": 65, "path": "/Users/<USER>/Work/drill2/onboard/build/can_msgs/rosidl_generator_c"}, {"backtrace": 65, "path": "/Users/<USER>/Work/drill2/onboard/build/can_msgs/rosidl_generator_py"}, {"backtrace": 13, "isSystem": true, "path": "/Users/<USER>/ros2_jazzy/install/std_msgs/include/std_msgs"}, {"backtrace": 13, "isSystem": true, "path": "/Users/<USER>/ros2_jazzy/install/builtin_interfaces/include/builtin_interfaces"}, {"backtrace": 13, "isSystem": true, "path": "/Users/<USER>/ros2_jazzy/install/rosidl_runtime_c/include/rosidl_runtime_c"}, {"backtrace": 13, "isSystem": true, "path": "/Users/<USER>/ros2_jazzy/install/rcutils/include/rcutils"}, {"backtrace": 13, "isSystem": true, "path": "/Users/<USER>/ros2_jazzy/install/rosidl_typesupport_interface/include/rosidl_typesupport_interface"}, {"backtrace": 9, "isSystem": true, "path": "/Users/<USER>/.ros2_venv/lib/python3.11/site-packages/numpy/_core/include"}, {"backtrace": 9, "isSystem": true, "path": "/opt/homebrew/opt/python@3.11/Frameworks/Python.framework/Versions/3.11/include/python3.11"}, {"backtrace": 11, "isSystem": true, "path": "/Users/<USER>/ros2_jazzy/install/fastcdr/include"}, {"backtrace": 11, "isSystem": true, "path": "/Users/<USER>/ros2_jazzy/install/rosidl_runtime_cpp/include/rosidl_runtime_cpp"}, {"backtrace": 11, "isSystem": true, "path": "/Users/<USER>/ros2_jazzy/install/rosidl_typesupport_fastrtps_cpp/include/rosidl_typesupport_fastrtps_cpp"}, {"backtrace": 11, "isSystem": true, "path": "/Users/<USER>/ros2_jazzy/install/rmw/include/rmw"}, {"backtrace": 11, "isSystem": true, "path": "/Users/<USER>/ros2_jazzy/install/rosidl_dynamic_typesupport/include/rosidl_dynamic_typesupport"}, {"backtrace": 11, "isSystem": true, "path": "/Users/<USER>/ros2_jazzy/install/rosidl_typesupport_fastrtps_c/include/rosidl_typesupport_fastrtps_c"}, {"backtrace": 11, "isSystem": true, "path": "/Users/<USER>/ros2_jazzy/install/rosidl_typesupport_introspection_c/include/rosidl_typesupport_introspection_c"}, {"backtrace": 11, "isSystem": true, "path": "/Users/<USER>/ros2_jazzy/install/rosidl_typesupport_introspection_cpp/include/rosidl_typesupport_introspection_cpp"}], "language": "C", "sourceIndexes": [0]}], "dependencies": [{"backtrace": 13, "id": "can_msgs__rosidl_generator_c::@6890427a1f51a3e7e1df"}, {"backtrace": 56, "id": "can_msgs__rosidl_typesupport_c::@6890427a1f51a3e7e1df"}, {"backtrace": 56, "id": "can_msgs__py::@fa8439080ea556237913"}], "id": "can_msgs__rosidl_generator_py::@6890427a1f51a3e7e1df", "install": {"destinations": [{"backtrace": 8, "path": "lib"}, {"backtrace": 8, "path": "lib"}], "prefix": {"path": "/Users/<USER>/Work/drill2/onboard/install/can_msgs"}}, "link": {"commandFragments": [{"fragment": "-dynamiclib -Wl,-headerpad_max_install_names", "role": "flags"}, {"backtrace": 9, "fragment": "-<PERSON><PERSON><PERSON>", "role": "flags"}, {"backtrace": 9, "fragment": "-undefined", "role": "flags"}, {"backtrace": 9, "fragment": "-<PERSON><PERSON><PERSON>", "role": "flags"}, {"backtrace": 9, "fragment": "dynamic_lookup", "role": "flags"}, {"fragment": "-Wl,-rpath,/Users/<USER>/Work/drill2/onboard/build/can_msgs -Wl,-rpath,/Users/<USER>/ros2_jazzy/install/std_msgs/lib -Wl,-rpath,/Users/<USER>/ros2_jazzy/install/builtin_interfaces/lib -Wl,-rpath,/Users/<USER>/ros2_jazzy/install/rosidl_typesupport_fastrtps_c/lib -Wl,-rpath,/Users/<USER>/ros2_jazzy/install/rosidl_typesupport_introspection_cpp/lib -Wl,-rpath,/Users/<USER>/ros2_jazzy/install/rosidl_typesupport_introspection_c/lib -Wl,-rpath,/Users/<USER>/ros2_jazzy/install/rosidl_typesupport_fastrtps_cpp/lib -Wl,-rpath,/Users/<USER>/ros2_jazzy/install/fastcdr/lib -Wl,-rpath,/Users/<USER>/ros2_jazzy/install/rmw/lib -Wl,-rpath,/Users/<USER>/ros2_jazzy/install/rosidl_dynamic_typesupport/lib -Wl,-rpath,/Users/<USER>/ros2_jazzy/install/rosidl_runtime_c/lib -Wl,-rpath,/Users/<USER>/ros2_jazzy/install/rcutils/lib", "role": "libraries"}, {"backtrace": 10, "fragment": "libcan_msgs__rosidl_typesupport_c.dylib", "role": "libraries"}, {"backtrace": 11, "fragment": "/Users/<USER>/ros2_jazzy/install/std_msgs/lib/libstd_msgs__rosidl_typesupport_fastrtps_c.dylib", "role": "libraries"}, {"backtrace": 11, "fragment": "/Users/<USER>/ros2_jazzy/install/std_msgs/lib/libstd_msgs__rosidl_typesupport_fastrtps_cpp.dylib", "role": "libraries"}, {"backtrace": 11, "fragment": "/Users/<USER>/ros2_jazzy/install/std_msgs/lib/libstd_msgs__rosidl_typesupport_introspection_c.dylib", "role": "libraries"}, {"backtrace": 11, "fragment": "/Users/<USER>/ros2_jazzy/install/std_msgs/lib/libstd_msgs__rosidl_typesupport_introspection_cpp.dylib", "role": "libraries"}, {"backtrace": 11, "fragment": "/Users/<USER>/ros2_jazzy/install/std_msgs/lib/libstd_msgs__rosidl_typesupport_cpp.dylib", "role": "libraries"}, {"backtrace": 11, "fragment": "/Users/<USER>/ros2_jazzy/install/std_msgs/lib/libstd_msgs__rosidl_generator_py.dylib", "role": "libraries"}, {"backtrace": 12, "fragment": "/Users/<USER>/ros2_jazzy/install/builtin_interfaces/lib/libbuiltin_interfaces__rosidl_typesupport_introspection_c.dylib", "role": "libraries"}, {"backtrace": 12, "fragment": "/Users/<USER>/ros2_jazzy/install/builtin_interfaces/lib/libbuiltin_interfaces__rosidl_typesupport_fastrtps_c.dylib", "role": "libraries"}, {"backtrace": 12, "fragment": "/Users/<USER>/ros2_jazzy/install/builtin_interfaces/lib/libbuiltin_interfaces__rosidl_typesupport_introspection_cpp.dylib", "role": "libraries"}, {"backtrace": 12, "fragment": "/Users/<USER>/ros2_jazzy/install/builtin_interfaces/lib/libbuiltin_interfaces__rosidl_typesupport_fastrtps_cpp.dylib", "role": "libraries"}, {"backtrace": 12, "fragment": "/Users/<USER>/ros2_jazzy/install/builtin_interfaces/lib/libbuiltin_interfaces__rosidl_typesupport_cpp.dylib", "role": "libraries"}, {"backtrace": 12, "fragment": "/Users/<USER>/ros2_jazzy/install/builtin_interfaces/lib/libbuiltin_interfaces__rosidl_generator_py.dylib", "role": "libraries"}, {"backtrace": 13, "fragment": "libcan_msgs__rosidl_generator_c.dylib", "role": "libraries"}, {"backtrace": 11, "fragment": "/Users/<USER>/ros2_jazzy/install/std_msgs/lib/libstd_msgs__rosidl_typesupport_c.dylib", "role": "libraries"}, {"backtrace": 11, "fragment": "/Users/<USER>/ros2_jazzy/install/std_msgs/lib/libstd_msgs__rosidl_generator_c.dylib", "role": "libraries"}, {"backtrace": 20, "fragment": "/Users/<USER>/ros2_jazzy/install/rosidl_typesupport_fastrtps_c/lib/librosidl_typesupport_fastrtps_c.dylib", "role": "libraries"}, {"backtrace": 23, "fragment": "/Users/<USER>/ros2_jazzy/install/rosidl_typesupport_introspection_cpp/lib/librosidl_typesupport_introspection_cpp.dylib", "role": "libraries"}, {"backtrace": 26, "fragment": "/Users/<USER>/ros2_jazzy/install/rosidl_typesupport_introspection_c/lib/librosidl_typesupport_introspection_c.dylib", "role": "libraries"}, {"backtrace": 20, "fragment": "/Users/<USER>/ros2_jazzy/install/rosidl_typesupport_fastrtps_cpp/lib/librosidl_typesupport_fastrtps_cpp.dylib", "role": "libraries"}, {"backtrace": 20, "fragment": "/Users/<USER>/ros2_jazzy/install/fastcdr/lib/libfastcdr.2.2.5.dylib", "role": "libraries"}, {"backtrace": 29, "fragment": "/Users/<USER>/ros2_jazzy/install/rmw/lib/librmw.dylib", "role": "libraries"}, {"backtrace": 52, "fragment": "/Users/<USER>/ros2_jazzy/install/rosidl_dynamic_typesupport/lib/librosidl_dynamic_typesupport.dylib", "role": "libraries"}, {"backtrace": 9, "fragment": "/opt/homebrew/opt/python@3.11/Frameworks/Python.framework/Versions/3.11/lib/libpython3.11.dylib", "role": "libraries"}, {"backtrace": 12, "fragment": "/Users/<USER>/ros2_jazzy/install/builtin_interfaces/lib/libbuiltin_interfaces__rosidl_typesupport_c.dylib", "role": "libraries"}, {"backtrace": 12, "fragment": "/Users/<USER>/ros2_jazzy/install/builtin_interfaces/lib/libbuiltin_interfaces__rosidl_generator_c.dylib", "role": "libraries"}, {"backtrace": 55, "fragment": "/Users/<USER>/ros2_jazzy/install/rosidl_runtime_c/lib/librosidl_runtime_c.dylib", "role": "libraries"}, {"backtrace": 55, "fragment": "/Users/<USER>/ros2_jazzy/install/rcutils/lib/librcutils.dylib", "role": "libraries"}], "language": "C"}, "name": "can_msgs__rosidl_generator_py", "nameOnDisk": "libcan_msgs__rosidl_generator_py.dylib", "paths": {"build": ".", "source": "."}, "sourceGroups": [{"name": "Source Files", "sourceIndexes": [0]}], "sources": [{"backtrace": 5, "compileGroupIndex": 0, "isGenerated": true, "path": "/Users/<USER>/Work/drill2/onboard/build/can_msgs/rosidl_generator_py/can_msgs/msg/_frame_s.c", "sourceGroupIndex": 0}], "type": "SHARED_LIBRARY"}