# This is the CMakeCache file.
# For build in directory: /Users/<USER>/Work/drill2/onboard/build/can_msgs
# It was generated by CMake: /opt/homebrew/bin/cmake
# You can edit this file to change values found and used by cmake.
# If you do not want to change any of the values, simply exit the editor.
# If you do want to change a value, simply edit, save, and exit the editor.
# The syntax for the file is as follows:
# KEY:TYPE=VALUE
# KEY is the name of a variable in the cache.
# TYPE is a hint to GUIs for the type of VALUE, DO NOT EDIT TYPE!.
# VALUE is the current value for the KEY.

########################
# EXTERNAL cache entries
########################

//Generate environment files in the CMAKE_INSTALL_PREFIX
AMENT_CMAKE_ENVIRONMENT_GENERATION:BOOL=OFF

//Generate environment files in the package share folder
AMENT_CMAKE_ENVIRONMENT_PACKAGE_GENERATION:BOOL=ON

//Generate marker file containing the parent prefix path
AMENT_CMAKE_ENVIRONMENT_PARENT_PREFIX_PATH_GENERATION:BOOL=ON

//Replace the CMake install command with a custom implementation
// using symlinks instead of copying resources
AMENT_CMAKE_SYMLINK_INSTALL:BOOL=1

//Generate an uninstall target to revert the effects of the install
// step
AMENT_CMAKE_UNINSTALL_TARGET:BOOL=ON

//The path where test results are generated
AMENT_TEST_RESULTS_DIR:PATH=/Users/<USER>/Work/drill2/onboard/build/can_msgs/test_results

//Global flag to cause add_library() to create shared libraries
// if on. If set to true, this will cause all libraries to be built
// shared unless the library was explicitly added as a static library.
BUILD_SHARED_LIBS:BOOL=ON

//Build the testing tree.
BUILD_TESTING:BOOL=ON

//Path to a program.
CMAKE_ADDR2LINE:FILEPATH=CMAKE_ADDR2LINE-NOTFOUND

//Path to a program.
CMAKE_AR:FILEPATH=/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/ar

//Choose the type of build, options are: None Debug Release RelWithDebInfo
// MinSizeRel ...
CMAKE_BUILD_TYPE:STRING=

//Enable/Disable color output during build.
CMAKE_COLOR_MAKEFILE:BOOL=ON

//CXX compiler
CMAKE_CXX_COMPILER:STRING=/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/c++

//Flags used by the CXX compiler during all build types.
CMAKE_CXX_FLAGS:STRING=-I/opt/homebrew/include

//Flags used by the CXX compiler during DEBUG builds.
CMAKE_CXX_FLAGS_DEBUG:STRING=-g

//Flags used by the CXX compiler during MINSIZEREL builds.
CMAKE_CXX_FLAGS_MINSIZEREL:STRING=-Os -DNDEBUG

//Flags used by the CXX compiler during RELEASE builds.
CMAKE_CXX_FLAGS_RELEASE:STRING=-O3 -DNDEBUG

//Flags used by the CXX compiler during RELWITHDEBINFO builds.
CMAKE_CXX_FLAGS_RELWITHDEBINFO:STRING=-O2 -g -DNDEBUG

//C compiler
CMAKE_C_COMPILER:STRING=/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/cc

//Flags used by the C compiler during all build types.
CMAKE_C_FLAGS:STRING=

//Flags used by the C compiler during DEBUG builds.
CMAKE_C_FLAGS_DEBUG:STRING=-g

//Flags used by the C compiler during MINSIZEREL builds.
CMAKE_C_FLAGS_MINSIZEREL:STRING=-Os -DNDEBUG

//Flags used by the C compiler during RELEASE builds.
CMAKE_C_FLAGS_RELEASE:STRING=-O3 -DNDEBUG

//Flags used by the C compiler during RELWITHDEBINFO builds.
CMAKE_C_FLAGS_RELWITHDEBINFO:STRING=-O2 -g -DNDEBUG

//Path to a program.
CMAKE_DLLTOOL:FILEPATH=CMAKE_DLLTOOL-NOTFOUND

//Flags used by the linker during all build types.
CMAKE_EXE_LINKER_FLAGS:STRING=

//Flags used by the linker during DEBUG builds.
CMAKE_EXE_LINKER_FLAGS_DEBUG:STRING=

//Flags used by the linker during MINSIZEREL builds.
CMAKE_EXE_LINKER_FLAGS_MINSIZEREL:STRING=

//Flags used by the linker during RELEASE builds.
CMAKE_EXE_LINKER_FLAGS_RELEASE:STRING=

//Flags used by the linker during RELWITHDEBINFO builds.
CMAKE_EXE_LINKER_FLAGS_RELWITHDEBINFO:STRING=

//Enable/Disable output of compile commands during generation.
CMAKE_EXPORT_COMPILE_COMMANDS:BOOL=

//Value Computed by CMake.
CMAKE_FIND_PACKAGE_REDIRECTS_DIR:STATIC=/Users/<USER>/Work/drill2/onboard/build/can_msgs/CMakeFiles/pkgRedirects

//Path to a program.
CMAKE_INSTALL_NAME_TOOL:FILEPATH=/usr/bin/install_name_tool

//Install path prefix, prepended onto install directories.
CMAKE_INSTALL_PREFIX:PATH=/Users/<USER>/Work/drill2/onboard/install/can_msgs

//Path to a program.
CMAKE_LINKER:FILEPATH=/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/ld

//Path to a program.
CMAKE_MAKE_PROGRAM:FILEPATH=/usr/bin/make

//Flags used by the linker during the creation of modules during
// all build types.
CMAKE_MODULE_LINKER_FLAGS:STRING=

//Flags used by the linker during the creation of modules during
// DEBUG builds.
CMAKE_MODULE_LINKER_FLAGS_DEBUG:STRING=

//Flags used by the linker during the creation of modules during
// MINSIZEREL builds.
CMAKE_MODULE_LINKER_FLAGS_MINSIZEREL:STRING=

//Flags used by the linker during the creation of modules during
// RELEASE builds.
CMAKE_MODULE_LINKER_FLAGS_RELEASE:STRING=

//Flags used by the linker during the creation of modules during
// RELWITHDEBINFO builds.
CMAKE_MODULE_LINKER_FLAGS_RELWITHDEBINFO:STRING=

//Path to a program.
CMAKE_NM:FILEPATH=/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/nm

//Path to a program.
CMAKE_OBJCOPY:FILEPATH=CMAKE_OBJCOPY-NOTFOUND

//Path to a program.
CMAKE_OBJDUMP:FILEPATH=/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/objdump

//Build architectures for OSX
CMAKE_OSX_ARCHITECTURES:STRING=

//Minimum OS X version to target for deployment (at runtime); newer
// APIs weak linked. Set to empty string for default value.
CMAKE_OSX_DEPLOYMENT_TARGET:STRING=

//The product will be built against the headers and libraries located
// inside the indicated SDK.
CMAKE_OSX_SYSROOT:PATH=/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk

//Value Computed by CMake
CMAKE_PROJECT_COMPAT_VERSION:STATIC=

//Value Computed by CMake
CMAKE_PROJECT_DESCRIPTION:STATIC=

//Value Computed by CMake
CMAKE_PROJECT_HOMEPAGE_URL:STATIC=

//Value Computed by CMake
CMAKE_PROJECT_NAME:STATIC=can_msgs

//Value Computed by CMake
CMAKE_PROJECT_VERSION:STATIC=

//Value Computed by CMake
CMAKE_PROJECT_VERSION_MAJOR:STATIC=

//Value Computed by CMake
CMAKE_PROJECT_VERSION_MINOR:STATIC=

//Value Computed by CMake
CMAKE_PROJECT_VERSION_PATCH:STATIC=

//Value Computed by CMake
CMAKE_PROJECT_VERSION_TWEAK:STATIC=

//Path to a program.
CMAKE_RANLIB:FILEPATH=/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/ranlib

//Path to a program.
CMAKE_READELF:FILEPATH=CMAKE_READELF-NOTFOUND

//Flags used by the linker during the creation of shared libraries
// during all build types.
CMAKE_SHARED_LINKER_FLAGS:STRING=

//Flags used by the linker during the creation of shared libraries
// during DEBUG builds.
CMAKE_SHARED_LINKER_FLAGS_DEBUG:STRING=

//Flags used by the linker during the creation of shared libraries
// during MINSIZEREL builds.
CMAKE_SHARED_LINKER_FLAGS_MINSIZEREL:STRING=

//Flags used by the linker during the creation of shared libraries
// during RELEASE builds.
CMAKE_SHARED_LINKER_FLAGS_RELEASE:STRING=

//Flags used by the linker during the creation of shared libraries
// during RELWITHDEBINFO builds.
CMAKE_SHARED_LINKER_FLAGS_RELWITHDEBINFO:STRING=

//If set, runtime paths are not added when installing shared libraries,
// but are added when building.
CMAKE_SKIP_INSTALL_RPATH:BOOL=NO

//If set, runtime paths are not added when using shared libraries.
CMAKE_SKIP_RPATH:BOOL=NO

//Flags used by the linker during the creation of static libraries
// during all build types.
CMAKE_STATIC_LINKER_FLAGS:STRING=

//Flags used by the linker during the creation of static libraries
// during DEBUG builds.
CMAKE_STATIC_LINKER_FLAGS_DEBUG:STRING=

//Flags used by the linker during the creation of static libraries
// during MINSIZEREL builds.
CMAKE_STATIC_LINKER_FLAGS_MINSIZEREL:STRING=

//Flags used by the linker during the creation of static libraries
// during RELEASE builds.
CMAKE_STATIC_LINKER_FLAGS_RELEASE:STRING=

//Flags used by the linker during the creation of static libraries
// during RELWITHDEBINFO builds.
CMAKE_STATIC_LINKER_FLAGS_RELWITHDEBINFO:STRING=

//Path to a program.
CMAKE_STRIP:FILEPATH=/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/strip

//Path to a program.
CMAKE_TAPI:FILEPATH=/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/tapi

//If this value is on, makefiles will be generated without the
// .SILENT directive, and all commands will be echoed to the console
// during the make.  This is useful for debugging only. With Visual
// Studio IDE projects all commands are done without /nologo.
CMAKE_VERBOSE_MAKEFILE:BOOL=FALSE

//Name of the computer/site where compile is being run
SITE:STRING=Romans-MacBook-Pro-5.local

//Path to a library.
_lib:FILEPATH=/Users/<USER>/ros2_jazzy/install/std_msgs/lib/libstd_msgs__rosidl_typesupport_fastrtps_cpp.dylib

//The directory containing a CMake configuration file for ament_cmake.
ament_cmake_DIR:PATH=/Users/<USER>/ros2_jazzy/install/ament_cmake/share/ament_cmake/cmake

//The directory containing a CMake configuration file for ament_cmake_copyright.
ament_cmake_copyright_DIR:PATH=/Users/<USER>/ros2_jazzy/install/ament_cmake_copyright/share/ament_cmake_copyright/cmake

//The directory containing a CMake configuration file for ament_cmake_core.
ament_cmake_core_DIR:PATH=/Users/<USER>/ros2_jazzy/install/ament_cmake_core/share/ament_cmake_core/cmake

//The directory containing a CMake configuration file for ament_cmake_cppcheck.
ament_cmake_cppcheck_DIR:PATH=/Users/<USER>/ros2_jazzy/install/ament_cmake_cppcheck/share/ament_cmake_cppcheck/cmake

//The directory containing a CMake configuration file for ament_cmake_cpplint.
ament_cmake_cpplint_DIR:PATH=/Users/<USER>/ros2_jazzy/install/ament_cmake_cpplint/share/ament_cmake_cpplint/cmake

//The directory containing a CMake configuration file for ament_cmake_export_definitions.
ament_cmake_export_definitions_DIR:PATH=/Users/<USER>/ros2_jazzy/install/ament_cmake_export_definitions/share/ament_cmake_export_definitions/cmake

//The directory containing a CMake configuration file for ament_cmake_export_dependencies.
ament_cmake_export_dependencies_DIR:PATH=/Users/<USER>/ros2_jazzy/install/ament_cmake_export_dependencies/share/ament_cmake_export_dependencies/cmake

//The directory containing a CMake configuration file for ament_cmake_export_include_directories.
ament_cmake_export_include_directories_DIR:PATH=/Users/<USER>/ros2_jazzy/install/ament_cmake_export_include_directories/share/ament_cmake_export_include_directories/cmake

//The directory containing a CMake configuration file for ament_cmake_export_interfaces.
ament_cmake_export_interfaces_DIR:PATH=/Users/<USER>/ros2_jazzy/install/ament_cmake_export_interfaces/share/ament_cmake_export_interfaces/cmake

//The directory containing a CMake configuration file for ament_cmake_export_libraries.
ament_cmake_export_libraries_DIR:PATH=/Users/<USER>/ros2_jazzy/install/ament_cmake_export_libraries/share/ament_cmake_export_libraries/cmake

//The directory containing a CMake configuration file for ament_cmake_export_link_flags.
ament_cmake_export_link_flags_DIR:PATH=/Users/<USER>/ros2_jazzy/install/ament_cmake_export_link_flags/share/ament_cmake_export_link_flags/cmake

//The directory containing a CMake configuration file for ament_cmake_export_targets.
ament_cmake_export_targets_DIR:PATH=/Users/<USER>/ros2_jazzy/install/ament_cmake_export_targets/share/ament_cmake_export_targets/cmake

//The directory containing a CMake configuration file for ament_cmake_flake8.
ament_cmake_flake8_DIR:PATH=/Users/<USER>/ros2_jazzy/install/ament_cmake_flake8/share/ament_cmake_flake8/cmake

//The directory containing a CMake configuration file for ament_cmake_gen_version_h.
ament_cmake_gen_version_h_DIR:PATH=/Users/<USER>/ros2_jazzy/install/ament_cmake_gen_version_h/share/ament_cmake_gen_version_h/cmake

//The directory containing a CMake configuration file for ament_cmake_gmock.
ament_cmake_gmock_DIR:PATH=/Users/<USER>/ros2_jazzy/install/ament_cmake_gmock/share/ament_cmake_gmock/cmake

//The directory containing a CMake configuration file for ament_cmake_gtest.
ament_cmake_gtest_DIR:PATH=/Users/<USER>/ros2_jazzy/install/ament_cmake_gtest/share/ament_cmake_gtest/cmake

//The directory containing a CMake configuration file for ament_cmake_include_directories.
ament_cmake_include_directories_DIR:PATH=/Users/<USER>/ros2_jazzy/install/ament_cmake_include_directories/share/ament_cmake_include_directories/cmake

//The directory containing a CMake configuration file for ament_cmake_libraries.
ament_cmake_libraries_DIR:PATH=/Users/<USER>/ros2_jazzy/install/ament_cmake_libraries/share/ament_cmake_libraries/cmake

//The directory containing a CMake configuration file for ament_cmake_lint_cmake.
ament_cmake_lint_cmake_DIR:PATH=/Users/<USER>/ros2_jazzy/install/ament_cmake_lint_cmake/share/ament_cmake_lint_cmake/cmake

//The directory containing a CMake configuration file for ament_cmake_pep257.
ament_cmake_pep257_DIR:PATH=/Users/<USER>/ros2_jazzy/install/ament_cmake_pep257/share/ament_cmake_pep257/cmake

//The directory containing a CMake configuration file for ament_cmake_pytest.
ament_cmake_pytest_DIR:PATH=/Users/<USER>/ros2_jazzy/install/ament_cmake_pytest/share/ament_cmake_pytest/cmake

//The directory containing a CMake configuration file for ament_cmake_python.
ament_cmake_python_DIR:PATH=/Users/<USER>/ros2_jazzy/install/ament_cmake_python/share/ament_cmake_python/cmake

//The directory containing a CMake configuration file for ament_cmake_ros.
ament_cmake_ros_DIR:PATH=/Users/<USER>/ros2_jazzy/install/ament_cmake_ros/share/ament_cmake_ros/cmake

//The directory containing a CMake configuration file for ament_cmake_target_dependencies.
ament_cmake_target_dependencies_DIR:PATH=/Users/<USER>/ros2_jazzy/install/ament_cmake_target_dependencies/share/ament_cmake_target_dependencies/cmake

//The directory containing a CMake configuration file for ament_cmake_test.
ament_cmake_test_DIR:PATH=/Users/<USER>/ros2_jazzy/install/ament_cmake_test/share/ament_cmake_test/cmake

//The directory containing a CMake configuration file for ament_cmake_uncrustify.
ament_cmake_uncrustify_DIR:PATH=/Users/<USER>/ros2_jazzy/install/ament_cmake_uncrustify/share/ament_cmake_uncrustify/cmake

//The directory containing a CMake configuration file for ament_cmake_version.
ament_cmake_version_DIR:PATH=/Users/<USER>/ros2_jazzy/install/ament_cmake_version/share/ament_cmake_version/cmake

//The directory containing a CMake configuration file for ament_cmake_xmllint.
ament_cmake_xmllint_DIR:PATH=/Users/<USER>/ros2_jazzy/install/ament_cmake_xmllint/share/ament_cmake_xmllint/cmake

//Path to a program.
ament_copyright_BIN:FILEPATH=/Users/<USER>/ros2_jazzy/install/ament_copyright/bin/ament_copyright

//Path to a program.
ament_cppcheck_BIN:FILEPATH=/Users/<USER>/ros2_jazzy/install/ament_cppcheck/bin/ament_cppcheck

//Path to a program.
ament_cpplint_BIN:FILEPATH=/Users/<USER>/ros2_jazzy/install/ament_cpplint/bin/ament_cpplint

//Path to a program.
ament_flake8_BIN:FILEPATH=/Users/<USER>/ros2_jazzy/install/ament_flake8/bin/ament_flake8

//The directory containing a CMake configuration file for ament_lint_auto.
ament_lint_auto_DIR:PATH=/Users/<USER>/ros2_jazzy/install/ament_lint_auto/share/ament_lint_auto/cmake

//Path to a program.
ament_lint_cmake_BIN:FILEPATH=/Users/<USER>/ros2_jazzy/install/ament_lint_cmake/bin/ament_lint_cmake

//The directory containing a CMake configuration file for ament_lint_common.
ament_lint_common_DIR:PATH=/Users/<USER>/ros2_jazzy/install/ament_lint_common/share/ament_lint_common/cmake

//Path to a program.
ament_pep257_BIN:FILEPATH=/Users/<USER>/ros2_jazzy/install/ament_pep257/bin/ament_pep257

//Path to a program.
ament_uncrustify_BIN:FILEPATH=/Users/<USER>/ros2_jazzy/install/ament_uncrustify/bin/ament_uncrustify

//Path to a program.
ament_xmllint_BIN:FILEPATH=/Users/<USER>/ros2_jazzy/install/ament_xmllint/bin/ament_xmllint

//The directory containing a CMake configuration file for builtin_interfaces.
builtin_interfaces_DIR:PATH=/Users/<USER>/ros2_jazzy/install/builtin_interfaces/share/builtin_interfaces/cmake

//Value Computed by CMake
can_msgs_BINARY_DIR:STATIC=/Users/<USER>/Work/drill2/onboard/build/can_msgs

//Value Computed by CMake
can_msgs_IS_TOP_LEVEL:STATIC=ON

//Value Computed by CMake
can_msgs_SOURCE_DIR:STATIC=/Users/<USER>/Work/drill2/onboard/src/can_msgs

//Dependencies for the target
can_msgs__rosidl_generator_py_LIB_DEPENDS:STATIC=general;can_msgs__rosidl_generator_c;general;Python3::Python;general;can_msgs__rosidl_typesupport_c;general;std_msgs::std_msgs__rosidl_generator_c;general;std_msgs::std_msgs__rosidl_typesupport_fastrtps_c;general;std_msgs::std_msgs__rosidl_typesupport_fastrtps_cpp;general;std_msgs::std_msgs__rosidl_typesupport_introspection_c;general;std_msgs::std_msgs__rosidl_typesupport_c;general;std_msgs::std_msgs__rosidl_typesupport_introspection_cpp;general;std_msgs::std_msgs__rosidl_typesupport_cpp;general;std_msgs::std_msgs__rosidl_generator_py;general;std_msgs::std_msgs__rosidl_generator_py;general;builtin_interfaces::builtin_interfaces__rosidl_generator_c;general;builtin_interfaces::builtin_interfaces__rosidl_typesupport_introspection_c;general;builtin_interfaces::builtin_interfaces__rosidl_typesupport_fastrtps_c;general;builtin_interfaces::builtin_interfaces__rosidl_typesupport_c;general;builtin_interfaces::builtin_interfaces__rosidl_typesupport_introspection_cpp;general;builtin_interfaces::builtin_interfaces__rosidl_typesupport_fastrtps_cpp;general;builtin_interfaces::builtin_interfaces__rosidl_typesupport_cpp;general;builtin_interfaces::builtin_interfaces__rosidl_generator_py;general;builtin_interfaces::builtin_interfaces__rosidl_generator_py;

//Dependencies for the target
can_msgs_s__rosidl_typesupport_c_LIB_DEPENDS:STATIC=general;can_msgs__rosidl_generator_py;general;can_msgs__rosidl_typesupport_c;general;can_msgs__rosidl_typesupport_c;general;rosidl_runtime_c::rosidl_runtime_c;general;rosidl_typesupport_c::rosidl_typesupport_c;general;/Users/<USER>/ros2_jazzy/install/rmw/lib/librmw.dylib;general;rcutils::rcutils;general;rosidl_dynamic_typesupport::rosidl_dynamic_typesupport;general;rosidl_runtime_c::rosidl_runtime_c;

//Dependencies for the target
can_msgs_s__rosidl_typesupport_fastrtps_c_LIB_DEPENDS:STATIC=general;can_msgs__rosidl_generator_py;general;can_msgs__rosidl_typesupport_fastrtps_c;general;can_msgs__rosidl_typesupport_c;general;rosidl_runtime_c::rosidl_runtime_c;general;rosidl_typesupport_c::rosidl_typesupport_c;general;/Users/<USER>/ros2_jazzy/install/rmw/lib/librmw.dylib;general;rcutils::rcutils;general;rosidl_dynamic_typesupport::rosidl_dynamic_typesupport;general;rosidl_runtime_c::rosidl_runtime_c;

//Dependencies for the target
can_msgs_s__rosidl_typesupport_introspection_c_LIB_DEPENDS:STATIC=general;can_msgs__rosidl_generator_py;general;can_msgs__rosidl_typesupport_introspection_c;general;can_msgs__rosidl_typesupport_c;general;rosidl_runtime_c::rosidl_runtime_c;general;rosidl_typesupport_c::rosidl_typesupport_c;general;/Users/<USER>/ros2_jazzy/install/rmw/lib/librmw.dylib;general;rcutils::rcutils;general;rosidl_dynamic_typesupport::rosidl_dynamic_typesupport;general;rosidl_runtime_c::rosidl_runtime_c;

//The directory containing a CMake configuration file for fastcdr.
fastcdr_DIR:PATH=/Users/<USER>/ros2_jazzy/install/fastcdr/lib/cmake/fastcdr

//The directory containing a CMake configuration file for fastrtps_cmake_module.
fastrtps_cmake_module_DIR:PATH=/Users/<USER>/ros2_jazzy/install/fastrtps_cmake_module/share/fastrtps_cmake_module/cmake

//The directory containing a CMake configuration file for python_cmake_module.
python_cmake_module_DIR:PATH=/Users/<USER>/ros2_jazzy/install/python_cmake_module/share/python_cmake_module/cmake

//The directory containing a CMake configuration file for rcpputils.
rcpputils_DIR:PATH=/Users/<USER>/ros2_jazzy/install/rcpputils/share/rcpputils/cmake

//The directory containing a CMake configuration file for rcutils.
rcutils_DIR:PATH=/Users/<USER>/ros2_jazzy/install/rcutils/share/rcutils/cmake

//The directory containing a CMake configuration file for rmw.
rmw_DIR:PATH=/Users/<USER>/ros2_jazzy/install/rmw/share/rmw/cmake

//The directory containing a CMake configuration file for rosidl_adapter.
rosidl_adapter_DIR:PATH=/Users/<USER>/ros2_jazzy/install/rosidl_adapter/share/rosidl_adapter/cmake

//The directory containing a CMake configuration file for rosidl_cmake.
rosidl_cmake_DIR:PATH=/Users/<USER>/ros2_jazzy/install/rosidl_cmake/share/rosidl_cmake/cmake

//The directory containing a CMake configuration file for rosidl_core_generators.
rosidl_core_generators_DIR:PATH=/Users/<USER>/ros2_jazzy/install/rosidl_core_generators/share/rosidl_core_generators/cmake

//The directory containing a CMake configuration file for rosidl_core_runtime.
rosidl_core_runtime_DIR:PATH=/Users/<USER>/ros2_jazzy/install/rosidl_core_runtime/share/rosidl_core_runtime/cmake

//The directory containing a CMake configuration file for rosidl_default_generators.
rosidl_default_generators_DIR:PATH=/Users/<USER>/ros2_jazzy/install/rosidl_default_generators/share/rosidl_default_generators/cmake

//The directory containing a CMake configuration file for rosidl_default_runtime.
rosidl_default_runtime_DIR:PATH=/Users/<USER>/ros2_jazzy/install/rosidl_default_runtime/share/rosidl_default_runtime/cmake

//The directory containing a CMake configuration file for rosidl_dynamic_typesupport.
rosidl_dynamic_typesupport_DIR:PATH=/Users/<USER>/ros2_jazzy/install/rosidl_dynamic_typesupport/share/rosidl_dynamic_typesupport/cmake

//The directory containing a CMake configuration file for rosidl_generator_c.
rosidl_generator_c_DIR:PATH=/Users/<USER>/ros2_jazzy/install/rosidl_generator_c/share/rosidl_generator_c/cmake

//The directory containing a CMake configuration file for rosidl_generator_cpp.
rosidl_generator_cpp_DIR:PATH=/Users/<USER>/ros2_jazzy/install/rosidl_generator_cpp/share/rosidl_generator_cpp/cmake

//The directory containing a CMake configuration file for rosidl_generator_py.
rosidl_generator_py_DIR:PATH=/Users/<USER>/ros2_jazzy/install/rosidl_generator_py/share/rosidl_generator_py/cmake

//The directory containing a CMake configuration file for rosidl_generator_type_description.
rosidl_generator_type_description_DIR:PATH=/Users/<USER>/ros2_jazzy/install/rosidl_generator_type_description/share/rosidl_generator_type_description/cmake

//The directory containing a CMake configuration file for rosidl_runtime_c.
rosidl_runtime_c_DIR:PATH=/Users/<USER>/ros2_jazzy/install/rosidl_runtime_c/share/rosidl_runtime_c/cmake

//The directory containing a CMake configuration file for rosidl_runtime_cpp.
rosidl_runtime_cpp_DIR:PATH=/Users/<USER>/ros2_jazzy/install/rosidl_runtime_cpp/share/rosidl_runtime_cpp/cmake

//The directory containing a CMake configuration file for rosidl_typesupport_c.
rosidl_typesupport_c_DIR:PATH=/Users/<USER>/ros2_jazzy/install/rosidl_typesupport_c/share/rosidl_typesupport_c/cmake

//The directory containing a CMake configuration file for rosidl_typesupport_cpp.
rosidl_typesupport_cpp_DIR:PATH=/Users/<USER>/ros2_jazzy/install/rosidl_typesupport_cpp/share/rosidl_typesupport_cpp/cmake

//The directory containing a CMake configuration file for rosidl_typesupport_fastrtps_c.
rosidl_typesupport_fastrtps_c_DIR:PATH=/Users/<USER>/ros2_jazzy/install/rosidl_typesupport_fastrtps_c/share/rosidl_typesupport_fastrtps_c/cmake

//The directory containing a CMake configuration file for rosidl_typesupport_fastrtps_cpp.
rosidl_typesupport_fastrtps_cpp_DIR:PATH=/Users/<USER>/ros2_jazzy/install/rosidl_typesupport_fastrtps_cpp/share/rosidl_typesupport_fastrtps_cpp/cmake

//The directory containing a CMake configuration file for rosidl_typesupport_interface.
rosidl_typesupport_interface_DIR:PATH=/Users/<USER>/ros2_jazzy/install/rosidl_typesupport_interface/share/rosidl_typesupport_interface/cmake

//The directory containing a CMake configuration file for rosidl_typesupport_introspection_c.
rosidl_typesupport_introspection_c_DIR:PATH=/Users/<USER>/ros2_jazzy/install/rosidl_typesupport_introspection_c/share/rosidl_typesupport_introspection_c/cmake

//The directory containing a CMake configuration file for rosidl_typesupport_introspection_cpp.
rosidl_typesupport_introspection_cpp_DIR:PATH=/Users/<USER>/ros2_jazzy/install/rosidl_typesupport_introspection_cpp/share/rosidl_typesupport_introspection_cpp/cmake

//The directory containing a CMake configuration file for std_msgs.
std_msgs_DIR:PATH=/Users/<USER>/ros2_jazzy/install/std_msgs/share/std_msgs/cmake

//Path to a program.
xmllint_BIN:FILEPATH=/usr/bin/xmllint


########################
# INTERNAL cache entries
########################

//ADVANCED property for variable: CMAKE_ADDR2LINE
CMAKE_ADDR2LINE-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_AR
CMAKE_AR-ADVANCED:INTERNAL=1
//This is the directory where this CMakeCache.txt was created
CMAKE_CACHEFILE_DIR:INTERNAL=/Users/<USER>/Work/drill2/onboard/build/can_msgs
//Major version of cmake used to create the current loaded cache
CMAKE_CACHE_MAJOR_VERSION:INTERNAL=4
//Minor version of cmake used to create the current loaded cache
CMAKE_CACHE_MINOR_VERSION:INTERNAL=1
//Patch version of cmake used to create the current loaded cache
CMAKE_CACHE_PATCH_VERSION:INTERNAL=1
//ADVANCED property for variable: CMAKE_COLOR_MAKEFILE
CMAKE_COLOR_MAKEFILE-ADVANCED:INTERNAL=1
//Path to CMake executable.
CMAKE_COMMAND:INTERNAL=/opt/homebrew/bin/cmake
//Path to cpack program executable.
CMAKE_CPACK_COMMAND:INTERNAL=/opt/homebrew/bin/cpack
//Path to ctest program executable.
CMAKE_CTEST_COMMAND:INTERNAL=/opt/homebrew/bin/ctest
//ADVANCED property for variable: CMAKE_CXX_COMPILER
CMAKE_CXX_COMPILER-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_CXX_FLAGS
CMAKE_CXX_FLAGS-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_CXX_FLAGS_DEBUG
CMAKE_CXX_FLAGS_DEBUG-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_CXX_FLAGS_MINSIZEREL
CMAKE_CXX_FLAGS_MINSIZEREL-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_CXX_FLAGS_RELEASE
CMAKE_CXX_FLAGS_RELEASE-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_CXX_FLAGS_RELWITHDEBINFO
CMAKE_CXX_FLAGS_RELWITHDEBINFO-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_C_COMPILER
CMAKE_C_COMPILER-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_C_FLAGS
CMAKE_C_FLAGS-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_C_FLAGS_DEBUG
CMAKE_C_FLAGS_DEBUG-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_C_FLAGS_MINSIZEREL
CMAKE_C_FLAGS_MINSIZEREL-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_C_FLAGS_RELEASE
CMAKE_C_FLAGS_RELEASE-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_C_FLAGS_RELWITHDEBINFO
CMAKE_C_FLAGS_RELWITHDEBINFO-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_DLLTOOL
CMAKE_DLLTOOL-ADVANCED:INTERNAL=1
//Path to cache edit program executable.
CMAKE_EDIT_COMMAND:INTERNAL=/opt/homebrew/bin/ccmake
//Executable file format
CMAKE_EXECUTABLE_FORMAT:INTERNAL=MACHO
//ADVANCED property for variable: CMAKE_EXE_LINKER_FLAGS
CMAKE_EXE_LINKER_FLAGS-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_EXE_LINKER_FLAGS_DEBUG
CMAKE_EXE_LINKER_FLAGS_DEBUG-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_EXE_LINKER_FLAGS_MINSIZEREL
CMAKE_EXE_LINKER_FLAGS_MINSIZEREL-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_EXE_LINKER_FLAGS_RELEASE
CMAKE_EXE_LINKER_FLAGS_RELEASE-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_EXE_LINKER_FLAGS_RELWITHDEBINFO
CMAKE_EXE_LINKER_FLAGS_RELWITHDEBINFO-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_EXPORT_COMPILE_COMMANDS
CMAKE_EXPORT_COMPILE_COMMANDS-ADVANCED:INTERNAL=1
//Name of external makefile project generator.
CMAKE_EXTRA_GENERATOR:INTERNAL=
//Name of generator.
CMAKE_GENERATOR:INTERNAL=Unix Makefiles
//Generator instance identifier.
CMAKE_GENERATOR_INSTANCE:INTERNAL=
//Name of generator platform.
CMAKE_GENERATOR_PLATFORM:INTERNAL=
//Name of generator toolset.
CMAKE_GENERATOR_TOOLSET:INTERNAL=
//Source directory with the top level CMakeLists.txt file for this
// project
CMAKE_HOME_DIRECTORY:INTERNAL=/Users/<USER>/Work/drill2/onboard/src/can_msgs
//ADVANCED property for variable: CMAKE_INSTALL_NAME_TOOL
CMAKE_INSTALL_NAME_TOOL-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_LINKER
CMAKE_LINKER-ADVANCED:INTERNAL=1
//Name of CMakeLists files to read
CMAKE_LIST_FILE_NAME:INTERNAL=CMakeLists.txt
//ADVANCED property for variable: CMAKE_MAKE_PROGRAM
CMAKE_MAKE_PROGRAM-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_MODULE_LINKER_FLAGS
CMAKE_MODULE_LINKER_FLAGS-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_MODULE_LINKER_FLAGS_DEBUG
CMAKE_MODULE_LINKER_FLAGS_DEBUG-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_MODULE_LINKER_FLAGS_MINSIZEREL
CMAKE_MODULE_LINKER_FLAGS_MINSIZEREL-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_MODULE_LINKER_FLAGS_RELEASE
CMAKE_MODULE_LINKER_FLAGS_RELEASE-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_MODULE_LINKER_FLAGS_RELWITHDEBINFO
CMAKE_MODULE_LINKER_FLAGS_RELWITHDEBINFO-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_NM
CMAKE_NM-ADVANCED:INTERNAL=1
//number of local generators
CMAKE_NUMBER_OF_MAKEFILES:INTERNAL=2
//ADVANCED property for variable: CMAKE_OBJCOPY
CMAKE_OBJCOPY-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_OBJDUMP
CMAKE_OBJDUMP-ADVANCED:INTERNAL=1
//Platform information initialized
CMAKE_PLATFORM_INFO_INITIALIZED:INTERNAL=1
//ADVANCED property for variable: CMAKE_RANLIB
CMAKE_RANLIB-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_READELF
CMAKE_READELF-ADVANCED:INTERNAL=1
//Path to CMake installation.
CMAKE_ROOT:INTERNAL=/opt/homebrew/share/cmake
//ADVANCED property for variable: CMAKE_SHARED_LINKER_FLAGS
CMAKE_SHARED_LINKER_FLAGS-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_SHARED_LINKER_FLAGS_DEBUG
CMAKE_SHARED_LINKER_FLAGS_DEBUG-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_SHARED_LINKER_FLAGS_MINSIZEREL
CMAKE_SHARED_LINKER_FLAGS_MINSIZEREL-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_SHARED_LINKER_FLAGS_RELEASE
CMAKE_SHARED_LINKER_FLAGS_RELEASE-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_SHARED_LINKER_FLAGS_RELWITHDEBINFO
CMAKE_SHARED_LINKER_FLAGS_RELWITHDEBINFO-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_SKIP_INSTALL_RPATH
CMAKE_SKIP_INSTALL_RPATH-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_SKIP_RPATH
CMAKE_SKIP_RPATH-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_STATIC_LINKER_FLAGS
CMAKE_STATIC_LINKER_FLAGS-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_STATIC_LINKER_FLAGS_DEBUG
CMAKE_STATIC_LINKER_FLAGS_DEBUG-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_STATIC_LINKER_FLAGS_MINSIZEREL
CMAKE_STATIC_LINKER_FLAGS_MINSIZEREL-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_STATIC_LINKER_FLAGS_RELEASE
CMAKE_STATIC_LINKER_FLAGS_RELEASE-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_STATIC_LINKER_FLAGS_RELWITHDEBINFO
CMAKE_STATIC_LINKER_FLAGS_RELWITHDEBINFO-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_STRIP
CMAKE_STRIP-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_TAPI
CMAKE_TAPI-ADVANCED:INTERNAL=1
//uname command
CMAKE_UNAME:INTERNAL=/usr/bin/uname
//ADVANCED property for variable: CMAKE_VERBOSE_MAKEFILE
CMAKE_VERBOSE_MAKEFILE-ADVANCED:INTERNAL=1
//Details about finding Python3
FIND_PACKAGE_MESSAGE_DETAILS_Python3:INTERNAL=[/Users/<USER>/.ros2_venv/bin/python3][/opt/homebrew/opt/python@3.11/Frameworks/Python.framework/Versions/3.11/include/python3.11][/opt/homebrew/opt/python@3.11/Frameworks/Python.framework/Versions/3.11/lib/libpython3.11.dylib][/Users/<USER>/.ros2_venv/lib/python3.11/site-packages/numpy/_core/include][found components: Interpreter Development NumPy Development.Module Development.Embed ][v3.11.12()]
//Details about finding PythonExtra
FIND_PACKAGE_MESSAGE_DETAILS_PythonExtra:INTERNAL=[/Users/<USER>/.ros2_venv/bin/python3][/Users/<USER>/.ros2_venv/bin/python3][v()]
//The directory for Python library installation. This needs to
// be in PYTHONPATH when 'setup.py install' is called.
PYTHON_INSTALL_DIR:INTERNAL=lib/python3.11/site-packages
//Compiler reason failure
_Python3_Compiler_REASON_FAILURE:INTERNAL=
_Python3_DEVELOPMENT_EMBED_SIGNATURE:INTERNAL=a5c22a177b4f3bfe93e03f9eaa24da5a
_Python3_DEVELOPMENT_MODULE_SIGNATURE:INTERNAL=6a7f1792b5640cff25175a32d8fd80d4
//Development reason failure
_Python3_Development_REASON_FAILURE:INTERNAL=
_Python3_EXECUTABLE:INTERNAL=/Users/<USER>/.ros2_venv/bin/python3
//Path to a file.
_Python3_INCLUDE_DIR:INTERNAL=/opt/homebrew/opt/python@3.11/Frameworks/Python.framework/Versions/3.11/include/python3.11
//Python3 Properties
_Python3_INTERPRETER_PROPERTIES:INTERNAL=Python;3;11;12;64;<none>;cpython-311-darwin;abi3;/opt/homebrew/opt/python@3.11/Frameworks/Python.framework/Versions/3.11/lib/python3.11;/Users/<USER>/.ros2_venv/lib/python3.11;/Users/<USER>/.ros2_venv/lib/python3.11/site-packages;/Users/<USER>/.ros2_venv/lib/python3.11/site-packages
_Python3_INTERPRETER_SIGNATURE:INTERNAL=00461813ff4f738380ea98939d44e658
//Path to a library.
_Python3_LIBRARY_RELEASE:INTERNAL=/opt/homebrew/opt/python@3.11/Frameworks/Python.framework/Versions/3.11/lib/libpython3.11.dylib
_Python3_NUMPY_SIGNATURE:INTERNAL=9a9266673c243e8d1611bea0bfbea98b
//Path to a file.
_Python3_NumPy_INCLUDE_DIR:INTERNAL=/Users/<USER>/.ros2_venv/lib/python3.11/site-packages/numpy/_core/include
//Index for unique symlink install targets
__AMENT_CMAKE_SYMLINK_INSTALL_TARGETS_INDEX:INTERNAL=3

