---
description: 
globs: 
alwaysApply: true
---
  Это проект бортового программное обеспечение роботизированного бурового станка под ROS 2.

  Использую лучшие практики ROS2.
  
  Мы наследуемся от BaseNode во всех наших нодах (base_node package). Нужно использовать log из BaseNode. 
  
  Мы никогда не используем параметры ROS (ros param). У нас есть свой сервер параметров, самописный (params_server package). Все параметры нод находятся в nodes.yaml.
  Нужно реализовывать возможность обновления параметров в новых нодах с использованием base_node.

  Мы делаем документацию на русском языке, а комментарии в коде на английском.

  Чем меньше строк кода, тем лучше. Делай код понятным, читаемым, коротким, оптимальным. Сначала думай.

  Действую как старший разработчик топ уровня.

  Всегда помни о задаче в целом и доделывай до конца.

  Заботься о красивой архитектуре.

Keep It Simple. 

When implementing changes, especially in an unfamiliar codebase:
First thoroughly understand the code structure and design patterns
Use the simplest possible solution to fix the issue

For package xml and similar use my name Roman <NAME_EMAIL>
license -- proprietary, company - Zyfra robotics 


To init terminal ros2 environment use alias ros='source /Users/<USER>/ros2_jazzy/activate_ros'

# Project-specific coding rules

- Prefer explicit, strict access for required fields in ROS 2 messages. Do NOT use getattr() for fixed fields defined in .msg. If the field is missing, crash early so issues surface immediately.
- Access configuration parameters via strict keys. Do NOT use .get() with defaults for required paths (e.g., global_params['system_flags']['arm_present']). Fail fast if a required parameter is missing or path is wrong.


